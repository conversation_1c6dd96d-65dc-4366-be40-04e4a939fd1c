{"name": "sought-monorepo", "private": true, "scripts": {"build": "turbo run build", "dev": "turbo run dev", "lint": "turbo run lint", "format": "prettier --write \"**/*.{ts,tsx,md}\"", "check-types": "turbo run check-types"}, "devDependencies": {"prettier": "^3.5.3", "turbo": "^2.5.4", "typescript": "5.8.2"}, "engines": {"node": ">=22"}, "volta": {"node": "22.16.0", "pnpm": "10.12.1"}, "packageManager": "pnpm@10.12.1", "workspaces": ["apps/*", "packages/*"], "dependencies": {"@nestjs/config": "^4.0.2"}}