{
  // Use IntelliSense to learn about possible attributes.
  // Hover to view descriptions of existing attributes.
  // For more information, visit: https://go.microsoft.com/fwlink/?linkid=830387
  "version": "0.2.0",
  "configurations": [
    {
      "name": "Python Debugger: Attach using Process Id",
      "type": "debugpy",
      "request": "attach",
      "processId": "${command:pickProcess}"
    },
    {
      "type": "node",
      "request": "launch",
      "name": "Debug Current TypeScript File",
      "skipFiles": [
        "<node_internals>/**"
      ],
      "runtimeExecutable": "node",
      "runtimeArgs": [
        "--require",
        "ts-node/register",
        "--require",
        "tsconfig-paths/register"
      ],
      "args": [
        "${file}"
      ],
      "cwd": "${workspaceFolder}/apps/server",
      "env": {
        "NODE_ENV": "development",
        "TS_NODE_PROJECT": "${workspaceFolder}/apps/server/tsconfig.json",
        "TS_NODE_COMPILER_OPTIONS": "{\"module\":\"commonjs\"}"
      },
      "console": "integratedTerminal",
      "sourceMaps": true
    },
    {
      "type": "node",
      "request": "launch",
      "name": "Debug Notion Indexing Script",
      "skipFiles": [
        "<node_internals>/**"
      ],
      "runtimeExecutable": "node",
      "runtimeArgs": [
        "--require",
        "ts-node/register",
        "--require",
        "tsconfig-paths/register"
      ],
      "args": [
        "${workspaceFolder}/apps/server/src/scripts/notion-indexing/indexNotionAccount.ts"
      ],
      "cwd": "${workspaceFolder}/apps/server",
      "env": {
        "NODE_ENV": "development",
        "TS_NODE_PROJECT": "${workspaceFolder}/apps/server/tsconfig.json",
        "TS_NODE_COMPILER_OPTIONS": "{\"module\":\"commonjs\"}"
      },
      "console": "integratedTerminal",
      "sourceMaps": true
    },
    {
      "type": "node",
      "request": "launch",
      "name": "Debug Notion Querying Script",
      "skipFiles": [
        "<node_internals>/**"
      ],
      "runtimeExecutable": "node",
      "runtimeArgs": [
        "--require",
        "ts-node/register",
        "--require",
        "tsconfig-paths/register"
      ],
      "args": [
        "${workspaceFolder}/apps/server/src/scripts/notion-indexing/queryNotionAccount.ts"
      ],
      "cwd": "${workspaceFolder}/apps/server",
      "env": {
        "NODE_ENV": "development",
        "TS_NODE_PROJECT": "${workspaceFolder}/apps/server/tsconfig.json",
        "TS_NODE_COMPILER_OPTIONS": "{\"module\":\"commonjs\"}"
      },
      "console": "integratedTerminal",
      "sourceMaps": true
    },
  ]
}