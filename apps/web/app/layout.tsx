import type { <PERSON>ada<PERSON> } from "next";
import { satoshi } from "./fonts";
import "./globals.css";

export const metadata: Metadata = {
  title: "SoughtAI",
  description: "When you sought, you find",
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en">
      <body className={`${satoshi.variable} font-satoshi dark`}>
        {children}
      </body>
    </html>
  );
}
