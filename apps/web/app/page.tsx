'use client';
import { useEffect, useRef } from 'react';
import { useChat } from '@ai-sdk/react';
import TextareaAutosize from 'react-textarea-autosize'
import {Send} from 'lucide-react';
import { Card, CardContent } from "@/components/ui/card";
import { ShineBorder } from "@/components/magicui/shine-border";
import { Button } from "@/components/ui/button";
import { textAreaStyles } from "@/components/ui/textarea";
import Message from '@/components/chat/Message';

export default function Home() {
  const { messages, handleInputChange, input, handleSubmit } = useChat();
  const formRef = useRef<HTMLFormElement>(null);
  const messageContainerRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (messageContainerRef.current) {
      messageContainerRef.current.scrollTop = messageContainerRef.current.scrollHeight;
    }
  }, [messages])

  return (
    <div className="w-screen h-screen">
      <div className="container h-full mx-auto">
        <div className="mx-auto h-full flex flex-col items-center lg:w-[800px] w-11/12">
          <div className="relative z-0 flex flex-col w-full grow max-h-[95vh] pb-25 overflow-hidden">
            <div ref={messageContainerRef} className="overflow-auto flex flex-col gap-4 h-full pt-4">
              {messages.map(message => {
                // todo eventually handle data response
                if (message.role === 'data' || message.role === 'system') {
                  return null;
                }
                return (
                  <Message 
                    role={message.role}
                    key={message.id}
                    content={message.parts.map(part => {
                      return part.type === 'text' ? part.text : null;
                    }).join('')}
                  />
                )
              })}
            </div>
          </div>
          <div className="fixed bottom-0 z-10 pb-4 bg-background lg:w-[800px] w-11/12">
            <Card className="relative overflow-hidden rounded-xl w-full py-2">
              <CardContent className="flex items-center gap-2">
                <ShineBorder className="rounded-2xl" shineColor={["#A07CFE", "#FE8FB5", "#FFBE7B"]} />
                <form 
                  ref={formRef}
                  onSubmit={handleSubmit} 
                  className="flex gap-x-2 items-center w-full"
                >
                  <div className="grow w-full flex">
                    <TextareaAutosize 
                      placeholder="Ask me stuff"
                      minRows={1}
                      name="prompt"
                      onChange={handleInputChange}
                      value={input}
                      className={textAreaStyles({className: 'resize-none w-full text-sm border-none bg-transparent'})} 
                      style={{background: 'transparent', height: 20}}
                      onKeyDown={e => {
                        if (e.key === 'Enter' && !e.shiftKey) {
                          e.preventDefault();
                          if (input.trim()) {
                            // Trigger the form's onSubmit handler
                            formRef.current?.requestSubmit();
                          }
                        }
                      }}
                    />
                  </div>
                  <div className="shrink flex">
                    <Button type="submit" size="icon">
                      <Send />
                    </Button>
                  </div>
                </form>
              </CardContent>
            </Card>  
          </div>
        </div>
      </div>
    </div>
  );
}
