'use client';

import { useState } from 'react';
import AgentChat from '@/components/chat/AgentChat';
import { Textarea } from '@/components/ui/textarea';

export default function Home() {
  const [description, setDescription] = useState('');
  return (
    <div className="container">
      <h1>Create an agent to talk about the stuff you're tired of talking about</h1>
      <Textarea value={description} onChange={e => setDescription(e.target.value)} />
    </div>
  );
}
