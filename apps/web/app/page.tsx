import { Button } from "@/components/ui/button";
import { prisma } from '@repo/database';

export default async function Home() {
  const res = await prisma.foo.findFirst({ where: { name: 'test' } });
  return (
    <div className="w-screen">
      <div className="container mx-auto flex flex-col gap-y-12 text-center justify-center items-center">
        <div className="p-12">
          <h1>hello world, {res?.name}</h1>
          <Button variant="default">This is a button</Button>
        </div>
        <div>test</div>
      </div>
    </div>
  );
}
