'use client';

import { useRef, useState } from 'react';
import { useChat } from '@ai-sdk/react';
import TextareaAutosize from 'react-textarea-autosize'
import {Send} from 'lucide-react';
import { Card, CardContent } from "@/components/ui/card";
import { ShineBorder } from "@/components/magicui/shine-border";
import { Button } from "@/components/ui/button";
import { textAreaStyles } from "@/components/ui/textarea";
import Message from '@/components/chat/Message';

export default function Home() {
  const { messages, sendMessage } = useChat();
  const [input, setInput] = useState('');
  const formRef = useRef<HTMLFormElement>(null);

  const handle

  return (
    <div className="w-screen h-screen">
      <div className="container h-full mx-auto">
        <div className="mx-auto h-full flex flex-col justify-center items-center max-w-[800px] py-6">
          <div className="flex flex-col w-full grow">
            {messages.map(message => {
              if (message.role === 'system') {
                return null;
              }
              return (
                <Message 
                  role={message.role}
                  key={message.id}
                  content={message.parts.map(part => {
                    return part.type === 'text' ? part.text : null;
                  }).join('')}
                />
              )
            })}
          </div>
          <div className="absolute bottom-4 md:w-[800px] w-11/12">
            <Card className="overflow-hidden w-full">
              <ShineBorder shineColor={["#A07CFE", "#FE8FB5", "#FFBE7B"]} />
              <CardContent className="flex items-center gap-2">
                <form 
                  ref={formRef}
                  onSubmit={e => {
                    e.preventDefault();
                    // @ts-expect-error dunno why typescript doesn't like this
                    const messageValue = e.target.elements.namedItem('prompt').value;
                    
                    sendMessage({text: messageValue});
                    setInput('');
                  }} 
                  className="flex gap-x-2 items-center w-full"
                >
                  <TextareaAutosize 
                    name="prompt"
                    onChange={e => setInput(e.target.value)}
                    value={input}
                    className={textAreaStyles({className: 'grow text-sm'})} 
                    onKeyDown={e => {
                      e.preventDefault();
                      if (e.key === 'Enter' && !e.shiftKey) {
                        if (input.trim()) {
                          formRef.current?.submit();
                        }
                      }
                    }}
                  />
                  <div className="h-full flex items-start justify-center">
                    <Button size="icon">
                      <Send />
                    </Button>
                  </div>
                </form>
              </CardContent>
            </Card>  
          </div>
        </div>
      </div>
    </div>
  );
}
