'use client';
import { useEffect, useRef, useState } from 'react';
import { useChat } from '@ai-sdk/react';
import TextareaAutosize from 'react-textarea-autosize'
import {Send} from 'lucide-react';
import { Card, CardContent } from "@/components/ui/card";
import { ShineBorder } from "@/components/magicui/shine-border";
import { Button } from "@/components/ui/button";
import { textAreaStyles } from "@/components/ui/textarea";
import Message from '@/components/chat/Message';

export default function Home() {
  const { messages, sendMessage } = useChat();
  const [input, setInput] = useState('');
  const formRef = useRef<HTMLFormElement>(null);
  const messageContainerRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (messageContainerRef.current) {
      messageContainerRef.current.scrollTop = messageContainerRef.current.scrollHeight;
    }
  }, [messages])

  console.log('messages -> ', messages)

  return (
    <div className="w-screen h-screen">
      <div className="container h-full mx-auto">
        <div className="mx-auto h-full flex flex-col items-center lg:w-[800px] w-11/12">
          <div className="relative z-0 flex flex-col w-full grow max-h-[85vh] pb-[62px] overflow-hidden">
            <div ref={messageContainerRef} className="overflow-auto flex flex-col gap-4 h-full pt-4">
              {messages.map(message => {
                if (message.role === 'system') {
                  return null;
                }
                return (
                  <Message 
                    role={message.role}
                    key={message.id}
                    content={message.parts.map(part => {
                      return part.type === 'text' ? part.text : null;
                    }).join('')}
                  />
                )
              })}
            </div>
          </div>
          <div className="fixed bottom-0 z-10 pb-4 bg-background lg:w-[800px] w-11/12">
            <Card className="relative overflow-hidden rounded-xl w-full">
              <CardContent className="flex items-center gap-2">
                <ShineBorder className="rounded-2xl" shineColor={["#A07CFE", "#FE8FB5", "#FFBE7B"]} />
                <form 
                  ref={formRef}
                  onSubmit={e => {
                    e.preventDefault();
                    // @ts-expect-error dunno why typescript doesn't like this
                    const messageValue = e.target.elements.namedItem('prompt').value;
                    
                    sendMessage({text: messageValue});
                    setInput('');
                  }} 
                  className="flex flex-col gap-y-2 items-center w-full"
                >
                  <TextareaAutosize 
                    placeholder="Ask me stuff"
                    name="prompt"
                    onChange={e => setInput(e.target.value)}
                    value={input}
                    className={textAreaStyles({className: 'resize-none grow text-sm border-none bg-transparent'})} 
                    style={{background: 'transparent'}}
                    onKeyDown={e => {
                      if (e.key === 'Enter' && !e.shiftKey) {
                        e.preventDefault();
                        if (input.trim()) {
                          // Trigger the form's onSubmit handler
                          formRef.current?.requestSubmit();
                        }
                      }
                    }}
                  />
                  <div className="h-full w-full flex justify-end">
                    <Button size="icon">
                      <Send />
                    </Button>
                  </div>
                </form>
              </CardContent>
            </Card>  
          </div>
        </div>
      </div>
    </div>
  );
}
