import {z} from 'zod';
import { openai } from '@ai-sdk/openai';
import { streamText, UIMessage, embed, tool } from 'ai';
import {prisma} from '@repo/database';

// Allow streaming responses up to 30 seconds
export const maxDuration = 30;

const inputSchema = z.object({
  query: z.string().describe("The query to search for"),
});

export async function POST(req: Request) {
  const { messages }: { messages: UIMessage[] } = await req.json();
  const result = streamText({
    model: openai('gpt-4o'),
    system: `You are a research assistant who uses the company's knowledge to answer questions from the user.`,
    messages,
    maxSteps: 5,
    tools: {
      searchKnowledgeGraph: tool({
        description: "Search the knowledge graph for the user",
        parameters: inputSchema,
        execute: async ({query}: {query: string}) => {
          const promptEmbeddings = await embed({
            model: openai.embedding('text-embedding-3-small'),
            value: query,
          });

          if (!promptEmbeddings) {
            return {results: []};
          }

          // Set search path to include extensions schema
          await prisma.$executeRaw`SET search_path TO extensions, public`;

          // Perform embeddings search using cosine similarity
          const similarDocuments = await prisma.$queryRaw`
            SELECT
              id,
              "parentId",
              type,
              text,
              metadata,
              (1 - (embedding <=> ${promptEmbeddings.embedding}::vector)) as similarity
            FROM "extensions"."TempEmbeddingsTable"
            WHERE embedding IS NOT NULL
            ORDER BY embedding <=> ${promptEmbeddings.embedding}::vector
            LIMIT 50
          ` as Array<{
            id: string;
            parentId: string | null;
            type: string | null;
            text: string | null;
            metadata: any;
            similarity: number;
          }>;

          return {results: similarDocuments};
        }
      }),
    },
  });

  return result.toDataStreamResponse();
}