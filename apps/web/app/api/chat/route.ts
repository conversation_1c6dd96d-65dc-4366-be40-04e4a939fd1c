import {z} from 'zod';
import { openai } from '@ai-sdk/openai';
import { streamText, UIMessage, convertToModelMessages, tool, embed } from 'ai';
import {prisma} from '@repo/database';

// Allow streaming responses up to 30 seconds
export const maxDuration = 30;

const outputSchema = z.object({
  id: z.string(),
  parentId: z.string().nullable(),
  type: z.string().nullable(),
  text: z.string().nullable(),
  metadata: z.any().nullable(),
  similarity: z.number(),
});

export async function POST(req: Request) {
  const { messages }: { messages: UIMessage[] } = await req.json();

  const result = streamText({
    model: openai('gpt-4o'),
    system: 'You are a helpful assistant. Please respond in markdown.',
    messages: convertToModelMessages(messages),
    tools: {
      searchKnowledgeGraph: tool({
        description: "Search the knowledge graph for the user",
        parameters: z.object({
          query: z.string().describe("The query to search for"),
        }),
        outputSchema: z.array(outputSchema),
        execute: async ({query}) => {
          const promptEmbeddings = await embed({
            model: openai.embedding('text-embedding-3-small'),
            value: query,
          });
  
          if (!promptEmbeddings) {
            return "No prompt embeddings";
          }
  
          // Set search path to include extensions schema
          await prisma.$executeRaw`SET search_path TO extensions, public`;

          // Perform embeddings search using cosine similarity
          const similarDocuments = await prisma.$queryRaw`
            SELECT
              id,
              "parentId",
              type,
              text,
              metadata,
              (1 - (embedding <=> ${promptEmbeddings.embedding}::vector)) as similarity
            FROM "extensions"."TempEmbeddingsTable"
            WHERE embedding IS NOT NULL
            ORDER BY embedding <=> ${promptEmbeddings.embedding}::vector
            LIMIT 50
          `;

          return similarDocuments as z.infer<typeof outputSchema>[];
        }
      })
    },
  });

  return result.toUIMessageStreamResponse();
}