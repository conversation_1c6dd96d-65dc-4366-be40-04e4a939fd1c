import ReactMarkdown from 'react-markdown';
import { cn } from "@/lib/utils";

const Message = ({content, role}: {content: string, role: 'assistant' | 'user'}) => {
  return (
    <div className={cn("whitespace-pre-wrap w-full flex", {
      'justify-end': role === 'user',
      'justify-start': role === 'assistant',
    })}>
      <div className={cn("text-sm max-w-3/4 flex flex-col rounded-full", {
        'bg-gray-800': role === 'user',
        'px-4 py-2': role === 'user',
      })}>
        <ReactMarkdown>
          {content}
        </ReactMarkdown>
      </div>
    </div>
  );
}

export default Message;