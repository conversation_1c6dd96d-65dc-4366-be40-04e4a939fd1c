{"name": "web", "version": "0.1.0", "type": "module", "private": true, "scripts": {"dev": "next dev --turbopack --port 3000", "build": "next build", "start": "next start", "lint": "next lint --max-warnings 0", "check-types": "tsc --noEmit"}, "dependencies": {"@radix-ui/react-slot": "^1.2.3", "@repo/database": "workspace:*", "@repo/package-template": "workspace:*", "@repo/ui": "workspace:*", "@tailwindcss/postcss": "^4.1.10", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cobe": "^0.6.4", "lucide-react": "^0.522.0", "motion": "^12.23.0", "next": "^15.3.0", "postcss": "^8.5.6", "react": "^19.1.0", "react-dom": "^19.1.0", "tailwind-merge": "^3.3.1", "tailwindcss": "^4.1.10"}, "devDependencies": {"@repo/eslint-config": "workspace:*", "@repo/typescript-config": "workspace:*", "@types/node": "^22.15.3", "@types/react": "19.1.0", "@types/react-dom": "19.1.1", "eslint": "^9.29.0", "tw-animate-css": "^1.3.4", "typescript": "5.8.2"}}