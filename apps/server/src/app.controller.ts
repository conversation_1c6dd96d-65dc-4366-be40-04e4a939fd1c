import { Controller, Get } from '@nestjs/common';
import {getMany<PERSON>oos, getOneFoo} from '@repo/data-access';
import { AppService } from './app.service';

@Controller()
export class AppController {
  constructor(private readonly appService: AppService) {}

  @Get()
  async getHello(): Promise<string> {
    const user = await getOneFoo({name: 'test'});
    // const user = await prisma.foo.findFirst({where: {name: 'test'}});
    console.log(user);
    return this.appService.getHello();
  }
}
