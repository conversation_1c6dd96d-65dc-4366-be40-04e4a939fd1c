// to run: turbo run index-notion

import { BlockObjectResponse } from "@notionhq/client";
import {prisma} from '@repo/database';
import { createKnowledgeNode, createNodeRelationship } from "@repo/data-access";
import { runScript } from "../runScript";
import { notion } from "../vendors/notion";
import { openAI } from "../vendors/openAI";

const PAGE_ENTRYPOINT_ID = '21a154a6b13d809e81bfdcb593f37f86';
const tristanUserId = 'c0202613-d5b0-4038-873d-9df9fac12de8';

// aggregate all blocks as a stream
// as we aggregate, need to handle different block types. If it's a database, for example, we need to walk that 
// database and aggregate data for its entries and properties
// at the end once we have the page structure, we can chunk them and create KnowledgeNodes
interface AggregatedContent {
  text: string;
  type: 'text' | 'database' | 'heading';
  level?: number; // for headings
  parentId?: string; // ID of the parent block/page
  blockId?: string; // ID of the current block
  metadata?: any; // for additional context
}

interface ChunkWithRelationships {
  content: string;
  type: 'fact' | 'decision';
  parentId?: string;
  blockId?: string;
  metadata?: any;
}

async function aggregateBlocksAsStream(pageId: string): Promise<AggregatedContent[]> {
  const aggregatedContent: AggregatedContent[] = [];

  async function processBlocks(parentId: string, depth = 0) {
    let cursor: string | undefined;

    while (true) {
      const response = await notion.blocks.children.list({
        block_id: parentId,
        start_cursor: cursor,
      });

      for (const block of response.results as BlockObjectResponse[]) {
        await processBlock(block, parentId, depth);
      }

      if (!response.has_more) break;
      cursor = response.next_cursor || undefined;
    }
  }

  async function processBlock(block: BlockObjectResponse, parentId: string, depth: number) {
    let content: AggregatedContent | null = null;

    switch (block.type) {
      case 'paragraph':
        if (block.paragraph?.rich_text?.length) {
          const text = block.paragraph.rich_text.map((rt: any) => rt.plain_text).join(' ');
          content = { text, type: 'text', parentId, blockId: block.id };
        }
        break;

      case 'heading_1':
        if (block.heading_1?.rich_text?.length) {
          const text = block.heading_1.rich_text.map((rt: any) => rt.plain_text).join(' ');
          content = { text, type: 'heading', level: 1, parentId, blockId: block.id };
        }
        break;

      case 'heading_2':
        if (block.heading_2?.rich_text?.length) {
          const text = block.heading_2.rich_text.map((rt: any) => rt.plain_text).join(' ');
          content = { text, type: 'heading', level: 2, parentId, blockId: block.id };
        }
        break;

      case 'heading_3':
        if (block.heading_3?.rich_text?.length) {
          const text = block.heading_3.rich_text.map((rt: any) => rt.plain_text).join(' ');
          content = { text, type: 'heading', level: 3, parentId, blockId: block.id };
        }
        break;

      case 'bulleted_list_item':
        if (block.bulleted_list_item?.rich_text?.length) {
          const text = `• ${block.bulleted_list_item.rich_text.map((rt: any) => rt.plain_text).join(' ')}`;
          content = { text, type: 'text', parentId, blockId: block.id };
        }
        break;

      case 'numbered_list_item':
        if (block.numbered_list_item?.rich_text?.length) {
          const text = `1. ${block.numbered_list_item.rich_text.map((rt: any) => rt.plain_text).join(' ')}`;
          content = { text, type: 'text', parentId, blockId: block.id };
        }
        break;

      case 'to_do':
        if (block.to_do?.rich_text?.length) {
          const checked = block.to_do.checked ? '[x]' : '[ ]';
          const text = `${checked} ${block.to_do.rich_text.map((rt: any) => rt.plain_text).join(' ')}`;
          content = { text, type: 'text', parentId, blockId: block.id };
        }
        break;

      case 'toggle':
        if (block.toggle?.rich_text?.length) {
          const text = block.toggle.rich_text.map((rt: any) => rt.plain_text).join(' ');
          content = { text, type: 'text', parentId, blockId: block.id };
        }
        break;

      case 'quote':
        if (block.quote?.rich_text?.length) {
          const text = `> ${block.quote.rich_text.map((rt: any) => rt.plain_text).join(' ')}`;
          content = { text, type: 'text', parentId, blockId: block.id };
        }
        break;

      case 'callout':
        if (block.callout?.rich_text?.length) {
          const icon = block.callout.icon?.type === 'emoji' ? block.callout.icon.emoji : '';
          const text = `${icon} ${block.callout.rich_text.map((rt: any) => rt.plain_text).join(' ')}`;
          content = { text, type: 'text', parentId, blockId: block.id };
        }
        break;

      case 'code':
        if (block.code?.rich_text?.length) {
          const language = block.code.language || '';
          const text = `\`\`\`${language}\n${block.code.rich_text.map((rt: any) => rt.plain_text).join('')}\n\`\`\``;
          content = { text, type: 'text', parentId, blockId: block.id };
        }
        break;

      case 'table_row':
        if (block.table_row?.cells?.length) {
          const text = block.table_row.cells
            .map(cell => cell.map((rt: any) => rt.plain_text).join(' '))
            .join(' | ');
          content = { text, type: 'text', parentId, blockId: block.id };
        }
        break;

      case 'child_database':
        // Add database title
        content = {
          text: `Database: ${block.child_database.title ?? 'unnamed database'}`,
          type: 'database',
          parentId,
          blockId: block.id,
          metadata: { databaseId: block.id }
        };

        // Walk the database and aggregate its content
        await walkDatabase(block.id);
        break;

      default:
        // Skip unsupported block types
        break;
    }

    if (content) {
      aggregatedContent.push(content);
    }

    // Recursively process child blocks if they exist
    if (block.has_children) {
      await processBlocks(block.id, depth + 1);
    }
  }

  async function walkDatabase(databaseId: string) {
    try {
      // Get database metadata
      const database = await notion.databases.retrieve({ database_id: databaseId });

      // Add database properties as content
      const properties = Object.entries(database.properties)
        .map(([name, prop]) => `${name} (${prop.type})`)
        .join(', ');

      aggregatedContent.push({
        text: `Database Properties: ${properties}`,
        type: 'database',
        metadata: { databaseId, properties: database.properties }
      });

      // Query database entries
      let cursor: string | undefined;
      while (true) {
        const response = await notion.databases.query({
          database_id: databaseId,
          start_cursor: cursor,
        });

        for (const page of response.results) {
          if ('properties' in page) {
            // Extract page title and properties
            const pageTitle = extractPageTitle(page.properties);
            const pageProperties = extractPageProperties(page.properties);

            aggregatedContent.push({
              text: `Entry: ${pageTitle}\n${pageProperties}`,
              type: 'database',
              metadata: { databaseId, pageId: page.id }
            });

            // Recursively process the page content
            await processBlocks(page.id);
          }
        }

        if (!response.has_more) break;
        cursor = response.next_cursor || undefined;
      }
    } catch (error) {
      console.error(`Error walking database ${databaseId}:`, error);
      aggregatedContent.push({
        text: `Database access error: ${databaseId}`,
        type: 'database',
        metadata: { databaseId, error: true }
      });
    }
  }

  function extractPageTitle(properties: any): string {
    // Look for title property
    for (const [key, value] of Object.entries(properties)) {
      if (value && typeof value === 'object' && 'type' in value && value.type === 'title') {
        return (value as any).title?.map((rt: any) => rt.plain_text).join(' ') || 'Untitled';
      }
    }
    return 'Untitled';
  }

  function extractPageProperties(properties: any): string {
    const props: string[] = [];

    for (const [key, value] of Object.entries(properties)) {
      if (value && typeof value === 'object' && 'type' in value) {
        const propValue = extractPropertyValue(value as any);
        if (propValue) {
          props.push(`${key}: ${propValue}`);
        }
      }
    }

    return props.join('\n');
  }

  function extractPropertyValue(property: any): string {
    switch (property.type) {
      case 'rich_text':
        return property.rich_text?.map((rt: any) => rt.plain_text).join(' ') || '';
      case 'title':
        return property.title?.map((rt: any) => rt.plain_text).join(' ') || '';
      case 'select':
        return property.select?.name || '';
      case 'multi_select':
        return property.multi_select?.map((s: any) => s.name).join(', ') || '';
      case 'date':
        return property.date?.start || '';
      case 'number':
        return property.number?.toString() || '';
      case 'checkbox':
        return property.checkbox ? 'Yes' : 'No';
      case 'url':
        return property.url || '';
      case 'email':
        return property.email || '';
      case 'phone_number':
        return property.phone_number || '';
      default:
        return '';
    }
  }

  // Start processing from the root page
  await processBlocks(pageId);

  return aggregatedContent;
}

runScript(async () => {
  console.log('Starting Notion indexing...');

  // Aggregate all content from the page structure
  const aggregatedContent = await aggregateBlocksAsStream(PAGE_ENTRYPOINT_ID);
  console.log(`Aggregated ${aggregatedContent.length} content items`);

  // // Chunk the aggregated content
  // const chunks = await chunkAggregatedContent(aggregatedContent);
  // console.log(`Created ${chunks.length} chunks`);

  // const createdNodes: { id: string; chunk: ChunkWithRelationships }[] = [];

  // Create KnowledgeNodes first
  for (const chunk of aggregatedContent) {
    const embedding = await openAI.embeddings.create({
      model: 'text-embedding-3-small',
      input: chunk.text,
    }).then(res => {
      if (!res.data[0]) {
        return null;
      }

      return res.data[0].embedding;
    });

    if (!embedding) {
      console.log('No embedding for chunk', chunk);
      continue;
    }

    await prisma.tempEmbeddingsTable.create({
      data: {
        parentId: chunk.parentId,
        type: chunk.type,
        text: chunk.text,
        metadata: chunk.metadata,
        embedding,
      }
    });
  }

  //   const knowledgeNode = await createKnowledgeNode({
  //     content: chunk.content,
  //     embedding,
  //     type: chunk.type,
  //     source: 'notion',
  //     // also we probably will want to be able to map a notion user to a user in our DB
  //     authorId: tristanUserId, // eventually need this to be dynamic
  //     context: {
  //       pageId: PAGE_ENTRYPOINT_ID,
  //       parentId: chunk.parentId,
  //       blockId: chunk.blockId,
  //       metadata: chunk.metadata
  //     },
  //   });

  //   createdNodes.push({ id: knowledgeNode.id, chunk });
  // }

  // console.log(`Created ${createdNodes.length} knowledge nodes`);

  // // Create NodeRelationships between chunks
  // for (let i = 0; i < createdNodes.length - 1; i++) {
  //   const currentNode = createdNodes[i];
  //   const nextNode = createdNodes[i + 1];

  //   if (!currentNode || !nextNode) continue;

  //   // Create a "supports" relationship between consecutive chunks
  //   await createNodeRelationship({
  //     fromId: currentNode.id,
  //     toId: nextNode.id,
  //     type: 'supports'
  //   });

  //   // If chunks share the same parent, create an additional relationship
  //   if (currentNode.chunk.parentId === nextNode.chunk.parentId) {
  //     await createNodeRelationship({
  //       fromId: currentNode.id,
  //       toId: nextNode.id,
  //       type: 'relates_to_topic'
  //     });
  //   }
  // }

  // console.log(`Created relationships between ${createdNodes.length - 1} node pairs`);
});