import { Agent, run } from '@openai/agents';
import { runScript } from '../runScript';

const PAGE_ENTRYPOINT_ID = '21a154a6b13d809e81bfdcb593f37f86';

runScript(async () => {
  const agent = new Agent({
    name: 'Assistant',
    instructions: 'You are a helpful assistant.',
  });
  
  const result = await run(
    agent,
    'Write a haiku about recursion in programming.',
  );
  
  console.log(result.finalOutput);
});