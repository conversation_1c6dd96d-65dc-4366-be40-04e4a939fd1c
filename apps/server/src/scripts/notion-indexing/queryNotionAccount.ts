// to run: turbo run search-notion --prompt "What is this doc about?"
import dedent from 'dedent';
import { Agent, run } from '@openai/agents';
import { prisma, supabase } from '@repo/database';
import { runScript } from '../runScript';
import { openAI } from '../vendors/openAI';

const PAGE_ENTRYPOINT_ID = '21a154a6b13d809e81bfdcb593f37f86';

// Parse command line arguments to get the --prompt flag
function getPromptFromArgs(): string {
  const args = process.argv.slice(2);
  const promptIndex = args.findIndex(arg => arg === '--prompt');

  if (promptIndex === -1 || promptIndex === args.length - 1) {
    return "What is this doc about?";
  }

  const prompt = args[promptIndex + 1];

  return prompt ?? "What is this doc about?";
}

runScript(async () => {
  const prompt = getPromptFromArgs();

  const promptEmbeddings = await openAI.embeddings.create({
    model: 'text-embedding-3-small',
    input: prompt,
  }).then(res => {
    if (!res.data[0]) {
      return null;
    }

    return res.data[0].embedding;
  });

  if (!promptEmbeddings) {
    console.log('No prompt embeddings');
    return;
  }

  // Set search path to include extensions schema
  await prisma.$executeRaw`SET search_path TO extensions, public`;

  // Perform embeddings search using cosine similarity
  const similarDocuments = await prisma.$queryRaw`
    SELECT
      id,
      "parentId",
      type,
      text,
      metadata,
      (1 - (embedding <=> ${promptEmbeddings}::vector)) as similarity
    FROM "extensions"."TempEmbeddingsTable"
    WHERE embedding IS NOT NULL
    ORDER BY embedding <=> ${promptEmbeddings}::vector
    LIMIT 50
  `;

  console.log('Similar documents found:', similarDocuments);

  const agent = new Agent({
    name: 'Assistant',
    instructions: dedent`
      You are a research assistant who has access to the knowledge graph of a cool startup. 
      Answer the question using only the following information: ${JSON.stringify(similarDocuments)}`,
  });

  const result = await run(
    agent,
    prompt,
  );

  console.log(result.finalOutput);
});