// to run: turbo run search-notion --prompt "What is this doc about?"
import { Agent, run } from '@openai/agents';
import { prisma } from '@repo/database';
import { runScript } from '../runScript';
import { openAI } from '../vendors/openAI';

const PAGE_ENTRYPOINT_ID = '21a154a6b13d809e81bfdcb593f37f86';

// Parse command line arguments to get the --prompt flag
function getPromptFromArgs(): string {
  const args = process.argv.slice(2);
  const promptIndex = args.findIndex(arg => arg === '--prompt');

  if (promptIndex === -1 || promptIndex === args.length - 1) {
    return "What is this doc about?";
  }

  const prompt = args[promptIndex + 1];

  return prompt ?? "What is this doc about?";
}

runScript(async () => {
  const prompt = getPromptFromArgs();

  const promptEmbeddings = await openAI.embeddings.create({
    model: 'text-embedding-3-small',
    input: prompt,
  }).then(res => {
    if (!res.data[0]) {
      return null;
    }

    return res.data[0].embedding;
  });

  if (!promptEmbeddings) {
    console.log('No prompt embeddings');
    return;
  }

  // Perform embeddings search using cosine similarity
  const similarDocuments = await prisma.$queryRaw`
    SELECT
      id,
      "parentId",
      type,
      text,
      metadata,
      (1 - (embedding <=> ${promptEmbeddings}::vector)) as similarity
    FROM "TempEmbeddingsTable"
    WHERE embedding IS NOT NULL
    ORDER BY embedding <=> ${promptEmbeddings}::vector
    LIMIT 50
  `;

  console.log('Similar documents found:', similarDocuments);

  const agent = new Agent({
    name: 'Assistant',
    instructions: 'You are a helpful assistant.',
  });

  const result = await run(
    agent,
    prompt,
  );

  console.log(result.finalOutput);
});