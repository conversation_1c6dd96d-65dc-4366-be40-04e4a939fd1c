import { Agent, run } from '@openai/agents';
import { runScript } from '../runScript';

const PAGE_ENTRYPOINT_ID = '21a154a6b13d809e81bfdcb593f37f86';

// Parse command line arguments to get the --prompt flag
function getPromptFromArgs(): string {
  const args = process.argv.slice(2);
  const promptIndex = args.findIndex(arg => arg === '--prompt');

  if (promptIndex === -1 || promptIndex === args.length - 1) {
    throw new Error('Please provide a prompt using the --prompt flag. Example: --prompt "Your question here"');
  }

  const prompt = args[promptIndex + 1];

  return prompt ?? 'Write a haiku about coding';
}

runScript(async () => {
  const prompt = getPromptFromArgs();

  const agent = new Agent({
    name: 'Assistant',
    instructions: 'You are a helpful assistant.',
  });

  const result = await run(
    agent,
    prompt,
  );

  console.log(result.finalOutput);
});