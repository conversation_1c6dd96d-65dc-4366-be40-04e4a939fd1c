import { z } from 'zod';
import { Prisma } from '../../database/generated/client';

/////////////////////////////////////////
// HELPER FUNCTIONS
/////////////////////////////////////////

// JSON
//------------------------------------------------------

export type NullableJsonInput = Prisma.JsonValue | null | 'JsonNull' | 'DbNull' | Prisma.NullTypes.DbNull | Prisma.NullTypes.JsonNull;

export const transformJsonNull = (v?: NullableJsonInput) => {
  if (!v || v === 'DbNull') return Prisma.DbNull;
  if (v === 'JsonNull') return Prisma.JsonNull;
  return v;
};

export const JsonValueSchema: z.ZodType<Prisma.JsonValue> = z.lazy(() =>
  z.union([
    z.string(),
    z.number(),
    z.boolean(),
    z.literal(null),
    z.record(z.lazy(() => JsonValueSchema.optional())),
    z.array(z.lazy(() => JsonValueSchema)),
  ])
);

export type JsonValueType = z.infer<typeof JsonValueSchema>;

export const NullableJsonValue = z
  .union([JsonValueSchema, z.literal('DbNull'), z.literal('JsonNull')])
  .nullable()
  .transform((v) => transformJsonNull(v));

export type NullableJsonValueType = z.infer<typeof NullableJsonValue>;

export const InputJsonValueSchema: z.ZodType<Prisma.InputJsonValue> = z.lazy(() =>
  z.union([
    z.string(),
    z.number(),
    z.boolean(),
    z.object({ toJSON: z.function(z.tuple([]), z.any()) }),
    z.record(z.lazy(() => z.union([InputJsonValueSchema, z.literal(null)]))),
    z.array(z.lazy(() => z.union([InputJsonValueSchema, z.literal(null)]))),
  ])
);

export type InputJsonValueType = z.infer<typeof InputJsonValueSchema>;


/////////////////////////////////////////
// ENUMS
/////////////////////////////////////////

export const TransactionIsolationLevelSchema = z.enum(['ReadUncommitted','ReadCommitted','RepeatableRead','Serializable']);

export const FooScalarFieldEnumSchema = z.enum(['id','name','email']);

export const TempEmbeddingsTableScalarFieldEnumSchema = z.enum(['id','parentId','type','text','metadata','createdAt','updatedAt']);

export const UserScalarFieldEnumSchema = z.enum(['userId']);

export const KnowledgeNodeScalarFieldEnumSchema = z.enum(['id','content','embedding','type','source','context','validated','createdAt','updatedAt','authorId']);

export const NodeRelationshipScalarFieldEnumSchema = z.enum(['id','fromId','toId','type','createdAt','updatedAt']);

export const ValidationScalarFieldEnumSchema = z.enum(['id','nodeId','expertId','notes','createdAt','updatedAt']);

export const TopicScalarFieldEnumSchema = z.enum(['id','name','createdAt','updatedAt']);

export const SortOrderSchema = z.enum(['asc','desc']);

export const NullableJsonNullValueInputSchema = z.enum(['DbNull','JsonNull',]).transform((value) => value === 'JsonNull' ? Prisma.JsonNull : value === 'DbNull' ? Prisma.DbNull : value);

export const QueryModeSchema = z.enum(['default','insensitive']);

export const JsonNullValueFilterSchema = z.enum(['DbNull','JsonNull','AnyNull',]).transform((value) => value === 'JsonNull' ? Prisma.JsonNull : value === 'DbNull' ? Prisma.JsonNull : value === 'AnyNull' ? Prisma.AnyNull : value);

export const NullsOrderSchema = z.enum(['first','last']);

export const SourceTypeSchema = z.enum(['slack','notion']);

export type SourceTypeType = `${z.infer<typeof SourceTypeSchema>}`

export const NodeTypeSchema = z.enum(['fact','decision','definition','question','answer']);

export type NodeTypeType = `${z.infer<typeof NodeTypeSchema>}`

export const RelationshipTypeSchema = z.enum(['answers','contradicts','supports','updates','authored_by','relates_to_topic','validated_by']);

export type RelationshipTypeType = `${z.infer<typeof RelationshipTypeSchema>}`

/////////////////////////////////////////
// MODELS
/////////////////////////////////////////

/////////////////////////////////////////
// FOO SCHEMA
/////////////////////////////////////////

export const FooSchema = z.object({
  id: z.number().int(),
  name: z.string(),
  email: z.string(),
})

export type Foo = z.infer<typeof FooSchema>

/////////////////////////////////////////
// TEMP EMBEDDINGS TABLE SCHEMA
/////////////////////////////////////////

export const TempEmbeddingsTableSchema = z.object({
  id: z.string().cuid(),
  parentId: z.string().nullable(),
  type: z.string().nullable(),
  text: z.string().nullable(),
  metadata: JsonValueSchema.nullable(),
  createdAt: z.coerce.date(),
  updatedAt: z.coerce.date(),
})

export type TempEmbeddingsTable = z.infer<typeof TempEmbeddingsTableSchema>

/////////////////////////////////////////
// USER SCHEMA
/////////////////////////////////////////

export const UserSchema = z.object({
  userId: z.string(),
})

export type User = z.infer<typeof UserSchema>

/////////////////////////////////////////
// KNOWLEDGE NODE SCHEMA
/////////////////////////////////////////

export const KnowledgeNodeSchema = z.object({
  type: NodeTypeSchema,
  source: SourceTypeSchema,
  id: z.string().cuid(),
  content: z.string(),
  embedding: z.number().array(),
  context: JsonValueSchema.nullable(),
  validated: z.boolean(),
  createdAt: z.coerce.date(),
  updatedAt: z.coerce.date(),
  authorId: z.string(),
})

export type KnowledgeNode = z.infer<typeof KnowledgeNodeSchema>

/////////////////////////////////////////
// NODE RELATIONSHIP SCHEMA
/////////////////////////////////////////

export const NodeRelationshipSchema = z.object({
  type: RelationshipTypeSchema,
  id: z.string().cuid(),
  fromId: z.string(),
  toId: z.string(),
  createdAt: z.coerce.date(),
  updatedAt: z.coerce.date(),
})

export type NodeRelationship = z.infer<typeof NodeRelationshipSchema>

/////////////////////////////////////////
// VALIDATION SCHEMA
/////////////////////////////////////////

export const ValidationSchema = z.object({
  id: z.string().cuid(),
  nodeId: z.string(),
  expertId: z.string(),
  notes: z.string().nullable(),
  createdAt: z.coerce.date(),
  updatedAt: z.coerce.date(),
})

export type Validation = z.infer<typeof ValidationSchema>

/////////////////////////////////////////
// TOPIC SCHEMA
/////////////////////////////////////////

export const TopicSchema = z.object({
  id: z.string().cuid(),
  name: z.string(),
  createdAt: z.coerce.date(),
  updatedAt: z.coerce.date(),
})

export type Topic = z.infer<typeof TopicSchema>

/////////////////////////////////////////
// SELECT & INCLUDE
/////////////////////////////////////////

// FOO
//------------------------------------------------------

export const FooSelectSchema: z.ZodType<Prisma.FooSelect> = z.object({
  id: z.boolean().optional(),
  name: z.boolean().optional(),
  email: z.boolean().optional(),
}).strict()

// TEMP EMBEDDINGS TABLE
//------------------------------------------------------

export const TempEmbeddingsTableSelectSchema: z.ZodType<Prisma.TempEmbeddingsTableSelect> = z.object({
  id: z.boolean().optional(),
  parentId: z.boolean().optional(),
  type: z.boolean().optional(),
  text: z.boolean().optional(),
  metadata: z.boolean().optional(),
  createdAt: z.boolean().optional(),
  updatedAt: z.boolean().optional(),
}).strict()

// USER
//------------------------------------------------------

export const UserIncludeSchema: z.ZodType<Prisma.UserInclude> = z.object({
  nodes: z.union([z.boolean(),z.lazy(() => KnowledgeNodeFindManyArgsSchema)]).optional(),
  validations: z.union([z.boolean(),z.lazy(() => ValidationFindManyArgsSchema)]).optional(),
  _count: z.union([z.boolean(),z.lazy(() => UserCountOutputTypeArgsSchema)]).optional(),
}).strict()

export const UserArgsSchema: z.ZodType<Prisma.UserDefaultArgs> = z.object({
  select: z.lazy(() => UserSelectSchema).optional(),
  include: z.lazy(() => UserIncludeSchema).optional(),
}).strict();

export const UserCountOutputTypeArgsSchema: z.ZodType<Prisma.UserCountOutputTypeDefaultArgs> = z.object({
  select: z.lazy(() => UserCountOutputTypeSelectSchema).nullish(),
}).strict();

export const UserCountOutputTypeSelectSchema: z.ZodType<Prisma.UserCountOutputTypeSelect> = z.object({
  nodes: z.boolean().optional(),
  validations: z.boolean().optional(),
}).strict();

export const UserSelectSchema: z.ZodType<Prisma.UserSelect> = z.object({
  userId: z.boolean().optional(),
  nodes: z.union([z.boolean(),z.lazy(() => KnowledgeNodeFindManyArgsSchema)]).optional(),
  validations: z.union([z.boolean(),z.lazy(() => ValidationFindManyArgsSchema)]).optional(),
  _count: z.union([z.boolean(),z.lazy(() => UserCountOutputTypeArgsSchema)]).optional(),
}).strict()

// KNOWLEDGE NODE
//------------------------------------------------------

export const KnowledgeNodeIncludeSchema: z.ZodType<Prisma.KnowledgeNodeInclude> = z.object({
  author: z.union([z.boolean(),z.lazy(() => UserArgsSchema)]).optional(),
  relationshipsFrom: z.union([z.boolean(),z.lazy(() => NodeRelationshipFindManyArgsSchema)]).optional(),
  relationshipsTo: z.union([z.boolean(),z.lazy(() => NodeRelationshipFindManyArgsSchema)]).optional(),
  validations: z.union([z.boolean(),z.lazy(() => ValidationFindManyArgsSchema)]).optional(),
  topics: z.union([z.boolean(),z.lazy(() => TopicFindManyArgsSchema)]).optional(),
  _count: z.union([z.boolean(),z.lazy(() => KnowledgeNodeCountOutputTypeArgsSchema)]).optional(),
}).strict()

export const KnowledgeNodeArgsSchema: z.ZodType<Prisma.KnowledgeNodeDefaultArgs> = z.object({
  select: z.lazy(() => KnowledgeNodeSelectSchema).optional(),
  include: z.lazy(() => KnowledgeNodeIncludeSchema).optional(),
}).strict();

export const KnowledgeNodeCountOutputTypeArgsSchema: z.ZodType<Prisma.KnowledgeNodeCountOutputTypeDefaultArgs> = z.object({
  select: z.lazy(() => KnowledgeNodeCountOutputTypeSelectSchema).nullish(),
}).strict();

export const KnowledgeNodeCountOutputTypeSelectSchema: z.ZodType<Prisma.KnowledgeNodeCountOutputTypeSelect> = z.object({
  relationshipsFrom: z.boolean().optional(),
  relationshipsTo: z.boolean().optional(),
  validations: z.boolean().optional(),
  topics: z.boolean().optional(),
}).strict();

export const KnowledgeNodeSelectSchema: z.ZodType<Prisma.KnowledgeNodeSelect> = z.object({
  id: z.boolean().optional(),
  content: z.boolean().optional(),
  embedding: z.boolean().optional(),
  type: z.boolean().optional(),
  source: z.boolean().optional(),
  context: z.boolean().optional(),
  validated: z.boolean().optional(),
  createdAt: z.boolean().optional(),
  updatedAt: z.boolean().optional(),
  authorId: z.boolean().optional(),
  author: z.union([z.boolean(),z.lazy(() => UserArgsSchema)]).optional(),
  relationshipsFrom: z.union([z.boolean(),z.lazy(() => NodeRelationshipFindManyArgsSchema)]).optional(),
  relationshipsTo: z.union([z.boolean(),z.lazy(() => NodeRelationshipFindManyArgsSchema)]).optional(),
  validations: z.union([z.boolean(),z.lazy(() => ValidationFindManyArgsSchema)]).optional(),
  topics: z.union([z.boolean(),z.lazy(() => TopicFindManyArgsSchema)]).optional(),
  _count: z.union([z.boolean(),z.lazy(() => KnowledgeNodeCountOutputTypeArgsSchema)]).optional(),
}).strict()

// NODE RELATIONSHIP
//------------------------------------------------------

export const NodeRelationshipIncludeSchema: z.ZodType<Prisma.NodeRelationshipInclude> = z.object({
  from: z.union([z.boolean(),z.lazy(() => KnowledgeNodeArgsSchema)]).optional(),
  to: z.union([z.boolean(),z.lazy(() => KnowledgeNodeArgsSchema)]).optional(),
}).strict()

export const NodeRelationshipArgsSchema: z.ZodType<Prisma.NodeRelationshipDefaultArgs> = z.object({
  select: z.lazy(() => NodeRelationshipSelectSchema).optional(),
  include: z.lazy(() => NodeRelationshipIncludeSchema).optional(),
}).strict();

export const NodeRelationshipSelectSchema: z.ZodType<Prisma.NodeRelationshipSelect> = z.object({
  id: z.boolean().optional(),
  fromId: z.boolean().optional(),
  toId: z.boolean().optional(),
  type: z.boolean().optional(),
  createdAt: z.boolean().optional(),
  updatedAt: z.boolean().optional(),
  from: z.union([z.boolean(),z.lazy(() => KnowledgeNodeArgsSchema)]).optional(),
  to: z.union([z.boolean(),z.lazy(() => KnowledgeNodeArgsSchema)]).optional(),
}).strict()

// VALIDATION
//------------------------------------------------------

export const ValidationIncludeSchema: z.ZodType<Prisma.ValidationInclude> = z.object({
  node: z.union([z.boolean(),z.lazy(() => KnowledgeNodeArgsSchema)]).optional(),
  expert: z.union([z.boolean(),z.lazy(() => UserArgsSchema)]).optional(),
}).strict()

export const ValidationArgsSchema: z.ZodType<Prisma.ValidationDefaultArgs> = z.object({
  select: z.lazy(() => ValidationSelectSchema).optional(),
  include: z.lazy(() => ValidationIncludeSchema).optional(),
}).strict();

export const ValidationSelectSchema: z.ZodType<Prisma.ValidationSelect> = z.object({
  id: z.boolean().optional(),
  nodeId: z.boolean().optional(),
  expertId: z.boolean().optional(),
  notes: z.boolean().optional(),
  createdAt: z.boolean().optional(),
  updatedAt: z.boolean().optional(),
  node: z.union([z.boolean(),z.lazy(() => KnowledgeNodeArgsSchema)]).optional(),
  expert: z.union([z.boolean(),z.lazy(() => UserArgsSchema)]).optional(),
}).strict()

// TOPIC
//------------------------------------------------------

export const TopicIncludeSchema: z.ZodType<Prisma.TopicInclude> = z.object({
  nodes: z.union([z.boolean(),z.lazy(() => KnowledgeNodeFindManyArgsSchema)]).optional(),
  _count: z.union([z.boolean(),z.lazy(() => TopicCountOutputTypeArgsSchema)]).optional(),
}).strict()

export const TopicArgsSchema: z.ZodType<Prisma.TopicDefaultArgs> = z.object({
  select: z.lazy(() => TopicSelectSchema).optional(),
  include: z.lazy(() => TopicIncludeSchema).optional(),
}).strict();

export const TopicCountOutputTypeArgsSchema: z.ZodType<Prisma.TopicCountOutputTypeDefaultArgs> = z.object({
  select: z.lazy(() => TopicCountOutputTypeSelectSchema).nullish(),
}).strict();

export const TopicCountOutputTypeSelectSchema: z.ZodType<Prisma.TopicCountOutputTypeSelect> = z.object({
  nodes: z.boolean().optional(),
}).strict();

export const TopicSelectSchema: z.ZodType<Prisma.TopicSelect> = z.object({
  id: z.boolean().optional(),
  name: z.boolean().optional(),
  createdAt: z.boolean().optional(),
  updatedAt: z.boolean().optional(),
  nodes: z.union([z.boolean(),z.lazy(() => KnowledgeNodeFindManyArgsSchema)]).optional(),
  _count: z.union([z.boolean(),z.lazy(() => TopicCountOutputTypeArgsSchema)]).optional(),
}).strict()


/////////////////////////////////////////
// INPUT TYPES
/////////////////////////////////////////

export const FooWhereInputSchema: z.ZodType<Prisma.FooWhereInput> = z.object({
  AND: z.union([ z.lazy(() => FooWhereInputSchema),z.lazy(() => FooWhereInputSchema).array() ]).optional(),
  OR: z.lazy(() => FooWhereInputSchema).array().optional(),
  NOT: z.union([ z.lazy(() => FooWhereInputSchema),z.lazy(() => FooWhereInputSchema).array() ]).optional(),
  id: z.union([ z.lazy(() => IntFilterSchema),z.number() ]).optional(),
  name: z.union([ z.lazy(() => StringFilterSchema),z.string() ]).optional(),
  email: z.union([ z.lazy(() => StringFilterSchema),z.string() ]).optional(),
}).strict();

export const FooOrderByWithRelationInputSchema: z.ZodType<Prisma.FooOrderByWithRelationInput> = z.object({
  id: z.lazy(() => SortOrderSchema).optional(),
  name: z.lazy(() => SortOrderSchema).optional(),
  email: z.lazy(() => SortOrderSchema).optional()
}).strict();

export const FooWhereUniqueInputSchema: z.ZodType<Prisma.FooWhereUniqueInput> = z.union([
  z.object({
    id: z.number().int(),
    name: z.string(),
    email: z.string()
  }),
  z.object({
    id: z.number().int(),
    name: z.string(),
  }),
  z.object({
    id: z.number().int(),
    email: z.string(),
  }),
  z.object({
    id: z.number().int(),
  }),
  z.object({
    name: z.string(),
    email: z.string(),
  }),
  z.object({
    name: z.string(),
  }),
  z.object({
    email: z.string(),
  }),
])
.and(z.object({
  id: z.number().int().optional(),
  name: z.string().optional(),
  email: z.string().optional(),
  AND: z.union([ z.lazy(() => FooWhereInputSchema),z.lazy(() => FooWhereInputSchema).array() ]).optional(),
  OR: z.lazy(() => FooWhereInputSchema).array().optional(),
  NOT: z.union([ z.lazy(() => FooWhereInputSchema),z.lazy(() => FooWhereInputSchema).array() ]).optional(),
}).strict());

export const FooOrderByWithAggregationInputSchema: z.ZodType<Prisma.FooOrderByWithAggregationInput> = z.object({
  id: z.lazy(() => SortOrderSchema).optional(),
  name: z.lazy(() => SortOrderSchema).optional(),
  email: z.lazy(() => SortOrderSchema).optional(),
  _count: z.lazy(() => FooCountOrderByAggregateInputSchema).optional(),
  _avg: z.lazy(() => FooAvgOrderByAggregateInputSchema).optional(),
  _max: z.lazy(() => FooMaxOrderByAggregateInputSchema).optional(),
  _min: z.lazy(() => FooMinOrderByAggregateInputSchema).optional(),
  _sum: z.lazy(() => FooSumOrderByAggregateInputSchema).optional()
}).strict();

export const FooScalarWhereWithAggregatesInputSchema: z.ZodType<Prisma.FooScalarWhereWithAggregatesInput> = z.object({
  AND: z.union([ z.lazy(() => FooScalarWhereWithAggregatesInputSchema),z.lazy(() => FooScalarWhereWithAggregatesInputSchema).array() ]).optional(),
  OR: z.lazy(() => FooScalarWhereWithAggregatesInputSchema).array().optional(),
  NOT: z.union([ z.lazy(() => FooScalarWhereWithAggregatesInputSchema),z.lazy(() => FooScalarWhereWithAggregatesInputSchema).array() ]).optional(),
  id: z.union([ z.lazy(() => IntWithAggregatesFilterSchema),z.number() ]).optional(),
  name: z.union([ z.lazy(() => StringWithAggregatesFilterSchema),z.string() ]).optional(),
  email: z.union([ z.lazy(() => StringWithAggregatesFilterSchema),z.string() ]).optional(),
}).strict();

export const TempEmbeddingsTableWhereInputSchema: z.ZodType<Prisma.TempEmbeddingsTableWhereInput> = z.object({
  AND: z.union([ z.lazy(() => TempEmbeddingsTableWhereInputSchema),z.lazy(() => TempEmbeddingsTableWhereInputSchema).array() ]).optional(),
  OR: z.lazy(() => TempEmbeddingsTableWhereInputSchema).array().optional(),
  NOT: z.union([ z.lazy(() => TempEmbeddingsTableWhereInputSchema),z.lazy(() => TempEmbeddingsTableWhereInputSchema).array() ]).optional(),
  id: z.union([ z.lazy(() => StringFilterSchema),z.string() ]).optional(),
  parentId: z.union([ z.lazy(() => StringNullableFilterSchema),z.string() ]).optional().nullable(),
  type: z.union([ z.lazy(() => StringNullableFilterSchema),z.string() ]).optional().nullable(),
  text: z.union([ z.lazy(() => StringNullableFilterSchema),z.string() ]).optional().nullable(),
  metadata: z.lazy(() => JsonNullableFilterSchema).optional(),
  createdAt: z.union([ z.lazy(() => DateTimeFilterSchema),z.coerce.date() ]).optional(),
  updatedAt: z.union([ z.lazy(() => DateTimeFilterSchema),z.coerce.date() ]).optional(),
}).strict();

export const TempEmbeddingsTableOrderByWithRelationInputSchema: z.ZodType<Prisma.TempEmbeddingsTableOrderByWithRelationInput> = z.object({
  id: z.lazy(() => SortOrderSchema).optional(),
  parentId: z.union([ z.lazy(() => SortOrderSchema),z.lazy(() => SortOrderInputSchema) ]).optional(),
  type: z.union([ z.lazy(() => SortOrderSchema),z.lazy(() => SortOrderInputSchema) ]).optional(),
  text: z.union([ z.lazy(() => SortOrderSchema),z.lazy(() => SortOrderInputSchema) ]).optional(),
  metadata: z.union([ z.lazy(() => SortOrderSchema),z.lazy(() => SortOrderInputSchema) ]).optional(),
  createdAt: z.lazy(() => SortOrderSchema).optional(),
  updatedAt: z.lazy(() => SortOrderSchema).optional()
}).strict();

export const TempEmbeddingsTableWhereUniqueInputSchema: z.ZodType<Prisma.TempEmbeddingsTableWhereUniqueInput> = z.object({
  id: z.string().cuid()
})
.and(z.object({
  id: z.string().cuid().optional(),
  AND: z.union([ z.lazy(() => TempEmbeddingsTableWhereInputSchema),z.lazy(() => TempEmbeddingsTableWhereInputSchema).array() ]).optional(),
  OR: z.lazy(() => TempEmbeddingsTableWhereInputSchema).array().optional(),
  NOT: z.union([ z.lazy(() => TempEmbeddingsTableWhereInputSchema),z.lazy(() => TempEmbeddingsTableWhereInputSchema).array() ]).optional(),
  parentId: z.union([ z.lazy(() => StringNullableFilterSchema),z.string() ]).optional().nullable(),
  type: z.union([ z.lazy(() => StringNullableFilterSchema),z.string() ]).optional().nullable(),
  text: z.union([ z.lazy(() => StringNullableFilterSchema),z.string() ]).optional().nullable(),
  metadata: z.lazy(() => JsonNullableFilterSchema).optional(),
  createdAt: z.union([ z.lazy(() => DateTimeFilterSchema),z.coerce.date() ]).optional(),
  updatedAt: z.union([ z.lazy(() => DateTimeFilterSchema),z.coerce.date() ]).optional(),
}).strict());

export const TempEmbeddingsTableOrderByWithAggregationInputSchema: z.ZodType<Prisma.TempEmbeddingsTableOrderByWithAggregationInput> = z.object({
  id: z.lazy(() => SortOrderSchema).optional(),
  parentId: z.union([ z.lazy(() => SortOrderSchema),z.lazy(() => SortOrderInputSchema) ]).optional(),
  type: z.union([ z.lazy(() => SortOrderSchema),z.lazy(() => SortOrderInputSchema) ]).optional(),
  text: z.union([ z.lazy(() => SortOrderSchema),z.lazy(() => SortOrderInputSchema) ]).optional(),
  metadata: z.union([ z.lazy(() => SortOrderSchema),z.lazy(() => SortOrderInputSchema) ]).optional(),
  createdAt: z.lazy(() => SortOrderSchema).optional(),
  updatedAt: z.lazy(() => SortOrderSchema).optional(),
  _count: z.lazy(() => TempEmbeddingsTableCountOrderByAggregateInputSchema).optional(),
  _max: z.lazy(() => TempEmbeddingsTableMaxOrderByAggregateInputSchema).optional(),
  _min: z.lazy(() => TempEmbeddingsTableMinOrderByAggregateInputSchema).optional()
}).strict();

export const TempEmbeddingsTableScalarWhereWithAggregatesInputSchema: z.ZodType<Prisma.TempEmbeddingsTableScalarWhereWithAggregatesInput> = z.object({
  AND: z.union([ z.lazy(() => TempEmbeddingsTableScalarWhereWithAggregatesInputSchema),z.lazy(() => TempEmbeddingsTableScalarWhereWithAggregatesInputSchema).array() ]).optional(),
  OR: z.lazy(() => TempEmbeddingsTableScalarWhereWithAggregatesInputSchema).array().optional(),
  NOT: z.union([ z.lazy(() => TempEmbeddingsTableScalarWhereWithAggregatesInputSchema),z.lazy(() => TempEmbeddingsTableScalarWhereWithAggregatesInputSchema).array() ]).optional(),
  id: z.union([ z.lazy(() => StringWithAggregatesFilterSchema),z.string() ]).optional(),
  parentId: z.union([ z.lazy(() => StringNullableWithAggregatesFilterSchema),z.string() ]).optional().nullable(),
  type: z.union([ z.lazy(() => StringNullableWithAggregatesFilterSchema),z.string() ]).optional().nullable(),
  text: z.union([ z.lazy(() => StringNullableWithAggregatesFilterSchema),z.string() ]).optional().nullable(),
  metadata: z.lazy(() => JsonNullableWithAggregatesFilterSchema).optional(),
  createdAt: z.union([ z.lazy(() => DateTimeWithAggregatesFilterSchema),z.coerce.date() ]).optional(),
  updatedAt: z.union([ z.lazy(() => DateTimeWithAggregatesFilterSchema),z.coerce.date() ]).optional(),
}).strict();

export const UserWhereInputSchema: z.ZodType<Prisma.UserWhereInput> = z.object({
  AND: z.union([ z.lazy(() => UserWhereInputSchema),z.lazy(() => UserWhereInputSchema).array() ]).optional(),
  OR: z.lazy(() => UserWhereInputSchema).array().optional(),
  NOT: z.union([ z.lazy(() => UserWhereInputSchema),z.lazy(() => UserWhereInputSchema).array() ]).optional(),
  userId: z.union([ z.lazy(() => StringFilterSchema),z.string() ]).optional(),
  nodes: z.lazy(() => KnowledgeNodeListRelationFilterSchema).optional(),
  validations: z.lazy(() => ValidationListRelationFilterSchema).optional()
}).strict();

export const UserOrderByWithRelationInputSchema: z.ZodType<Prisma.UserOrderByWithRelationInput> = z.object({
  userId: z.lazy(() => SortOrderSchema).optional(),
  nodes: z.lazy(() => KnowledgeNodeOrderByRelationAggregateInputSchema).optional(),
  validations: z.lazy(() => ValidationOrderByRelationAggregateInputSchema).optional()
}).strict();

export const UserWhereUniqueInputSchema: z.ZodType<Prisma.UserWhereUniqueInput> = z.object({
  userId: z.string()
})
.and(z.object({
  userId: z.string().optional(),
  AND: z.union([ z.lazy(() => UserWhereInputSchema),z.lazy(() => UserWhereInputSchema).array() ]).optional(),
  OR: z.lazy(() => UserWhereInputSchema).array().optional(),
  NOT: z.union([ z.lazy(() => UserWhereInputSchema),z.lazy(() => UserWhereInputSchema).array() ]).optional(),
  nodes: z.lazy(() => KnowledgeNodeListRelationFilterSchema).optional(),
  validations: z.lazy(() => ValidationListRelationFilterSchema).optional()
}).strict());

export const UserOrderByWithAggregationInputSchema: z.ZodType<Prisma.UserOrderByWithAggregationInput> = z.object({
  userId: z.lazy(() => SortOrderSchema).optional(),
  _count: z.lazy(() => UserCountOrderByAggregateInputSchema).optional(),
  _max: z.lazy(() => UserMaxOrderByAggregateInputSchema).optional(),
  _min: z.lazy(() => UserMinOrderByAggregateInputSchema).optional()
}).strict();

export const UserScalarWhereWithAggregatesInputSchema: z.ZodType<Prisma.UserScalarWhereWithAggregatesInput> = z.object({
  AND: z.union([ z.lazy(() => UserScalarWhereWithAggregatesInputSchema),z.lazy(() => UserScalarWhereWithAggregatesInputSchema).array() ]).optional(),
  OR: z.lazy(() => UserScalarWhereWithAggregatesInputSchema).array().optional(),
  NOT: z.union([ z.lazy(() => UserScalarWhereWithAggregatesInputSchema),z.lazy(() => UserScalarWhereWithAggregatesInputSchema).array() ]).optional(),
  userId: z.union([ z.lazy(() => StringWithAggregatesFilterSchema),z.string() ]).optional(),
}).strict();

export const KnowledgeNodeWhereInputSchema: z.ZodType<Prisma.KnowledgeNodeWhereInput> = z.object({
  AND: z.union([ z.lazy(() => KnowledgeNodeWhereInputSchema),z.lazy(() => KnowledgeNodeWhereInputSchema).array() ]).optional(),
  OR: z.lazy(() => KnowledgeNodeWhereInputSchema).array().optional(),
  NOT: z.union([ z.lazy(() => KnowledgeNodeWhereInputSchema),z.lazy(() => KnowledgeNodeWhereInputSchema).array() ]).optional(),
  id: z.union([ z.lazy(() => StringFilterSchema),z.string() ]).optional(),
  content: z.union([ z.lazy(() => StringFilterSchema),z.string() ]).optional(),
  embedding: z.lazy(() => FloatNullableListFilterSchema).optional(),
  type: z.union([ z.lazy(() => EnumNodeTypeFilterSchema),z.lazy(() => NodeTypeSchema) ]).optional(),
  source: z.union([ z.lazy(() => EnumSourceTypeFilterSchema),z.lazy(() => SourceTypeSchema) ]).optional(),
  context: z.lazy(() => JsonNullableFilterSchema).optional(),
  validated: z.union([ z.lazy(() => BoolFilterSchema),z.boolean() ]).optional(),
  createdAt: z.union([ z.lazy(() => DateTimeFilterSchema),z.coerce.date() ]).optional(),
  updatedAt: z.union([ z.lazy(() => DateTimeFilterSchema),z.coerce.date() ]).optional(),
  authorId: z.union([ z.lazy(() => StringFilterSchema),z.string() ]).optional(),
  author: z.union([ z.lazy(() => UserScalarRelationFilterSchema),z.lazy(() => UserWhereInputSchema) ]).optional(),
  relationshipsFrom: z.lazy(() => NodeRelationshipListRelationFilterSchema).optional(),
  relationshipsTo: z.lazy(() => NodeRelationshipListRelationFilterSchema).optional(),
  validations: z.lazy(() => ValidationListRelationFilterSchema).optional(),
  topics: z.lazy(() => TopicListRelationFilterSchema).optional()
}).strict();

export const KnowledgeNodeOrderByWithRelationInputSchema: z.ZodType<Prisma.KnowledgeNodeOrderByWithRelationInput> = z.object({
  id: z.lazy(() => SortOrderSchema).optional(),
  content: z.lazy(() => SortOrderSchema).optional(),
  embedding: z.lazy(() => SortOrderSchema).optional(),
  type: z.lazy(() => SortOrderSchema).optional(),
  source: z.lazy(() => SortOrderSchema).optional(),
  context: z.union([ z.lazy(() => SortOrderSchema),z.lazy(() => SortOrderInputSchema) ]).optional(),
  validated: z.lazy(() => SortOrderSchema).optional(),
  createdAt: z.lazy(() => SortOrderSchema).optional(),
  updatedAt: z.lazy(() => SortOrderSchema).optional(),
  authorId: z.lazy(() => SortOrderSchema).optional(),
  author: z.lazy(() => UserOrderByWithRelationInputSchema).optional(),
  relationshipsFrom: z.lazy(() => NodeRelationshipOrderByRelationAggregateInputSchema).optional(),
  relationshipsTo: z.lazy(() => NodeRelationshipOrderByRelationAggregateInputSchema).optional(),
  validations: z.lazy(() => ValidationOrderByRelationAggregateInputSchema).optional(),
  topics: z.lazy(() => TopicOrderByRelationAggregateInputSchema).optional()
}).strict();

export const KnowledgeNodeWhereUniqueInputSchema: z.ZodType<Prisma.KnowledgeNodeWhereUniqueInput> = z.object({
  id: z.string().cuid()
})
.and(z.object({
  id: z.string().cuid().optional(),
  AND: z.union([ z.lazy(() => KnowledgeNodeWhereInputSchema),z.lazy(() => KnowledgeNodeWhereInputSchema).array() ]).optional(),
  OR: z.lazy(() => KnowledgeNodeWhereInputSchema).array().optional(),
  NOT: z.union([ z.lazy(() => KnowledgeNodeWhereInputSchema),z.lazy(() => KnowledgeNodeWhereInputSchema).array() ]).optional(),
  content: z.union([ z.lazy(() => StringFilterSchema),z.string() ]).optional(),
  embedding: z.lazy(() => FloatNullableListFilterSchema).optional(),
  type: z.union([ z.lazy(() => EnumNodeTypeFilterSchema),z.lazy(() => NodeTypeSchema) ]).optional(),
  source: z.union([ z.lazy(() => EnumSourceTypeFilterSchema),z.lazy(() => SourceTypeSchema) ]).optional(),
  context: z.lazy(() => JsonNullableFilterSchema).optional(),
  validated: z.union([ z.lazy(() => BoolFilterSchema),z.boolean() ]).optional(),
  createdAt: z.union([ z.lazy(() => DateTimeFilterSchema),z.coerce.date() ]).optional(),
  updatedAt: z.union([ z.lazy(() => DateTimeFilterSchema),z.coerce.date() ]).optional(),
  authorId: z.union([ z.lazy(() => StringFilterSchema),z.string() ]).optional(),
  author: z.union([ z.lazy(() => UserScalarRelationFilterSchema),z.lazy(() => UserWhereInputSchema) ]).optional(),
  relationshipsFrom: z.lazy(() => NodeRelationshipListRelationFilterSchema).optional(),
  relationshipsTo: z.lazy(() => NodeRelationshipListRelationFilterSchema).optional(),
  validations: z.lazy(() => ValidationListRelationFilterSchema).optional(),
  topics: z.lazy(() => TopicListRelationFilterSchema).optional()
}).strict());

export const KnowledgeNodeOrderByWithAggregationInputSchema: z.ZodType<Prisma.KnowledgeNodeOrderByWithAggregationInput> = z.object({
  id: z.lazy(() => SortOrderSchema).optional(),
  content: z.lazy(() => SortOrderSchema).optional(),
  embedding: z.lazy(() => SortOrderSchema).optional(),
  type: z.lazy(() => SortOrderSchema).optional(),
  source: z.lazy(() => SortOrderSchema).optional(),
  context: z.union([ z.lazy(() => SortOrderSchema),z.lazy(() => SortOrderInputSchema) ]).optional(),
  validated: z.lazy(() => SortOrderSchema).optional(),
  createdAt: z.lazy(() => SortOrderSchema).optional(),
  updatedAt: z.lazy(() => SortOrderSchema).optional(),
  authorId: z.lazy(() => SortOrderSchema).optional(),
  _count: z.lazy(() => KnowledgeNodeCountOrderByAggregateInputSchema).optional(),
  _avg: z.lazy(() => KnowledgeNodeAvgOrderByAggregateInputSchema).optional(),
  _max: z.lazy(() => KnowledgeNodeMaxOrderByAggregateInputSchema).optional(),
  _min: z.lazy(() => KnowledgeNodeMinOrderByAggregateInputSchema).optional(),
  _sum: z.lazy(() => KnowledgeNodeSumOrderByAggregateInputSchema).optional()
}).strict();

export const KnowledgeNodeScalarWhereWithAggregatesInputSchema: z.ZodType<Prisma.KnowledgeNodeScalarWhereWithAggregatesInput> = z.object({
  AND: z.union([ z.lazy(() => KnowledgeNodeScalarWhereWithAggregatesInputSchema),z.lazy(() => KnowledgeNodeScalarWhereWithAggregatesInputSchema).array() ]).optional(),
  OR: z.lazy(() => KnowledgeNodeScalarWhereWithAggregatesInputSchema).array().optional(),
  NOT: z.union([ z.lazy(() => KnowledgeNodeScalarWhereWithAggregatesInputSchema),z.lazy(() => KnowledgeNodeScalarWhereWithAggregatesInputSchema).array() ]).optional(),
  id: z.union([ z.lazy(() => StringWithAggregatesFilterSchema),z.string() ]).optional(),
  content: z.union([ z.lazy(() => StringWithAggregatesFilterSchema),z.string() ]).optional(),
  embedding: z.lazy(() => FloatNullableListFilterSchema).optional(),
  type: z.union([ z.lazy(() => EnumNodeTypeWithAggregatesFilterSchema),z.lazy(() => NodeTypeSchema) ]).optional(),
  source: z.union([ z.lazy(() => EnumSourceTypeWithAggregatesFilterSchema),z.lazy(() => SourceTypeSchema) ]).optional(),
  context: z.lazy(() => JsonNullableWithAggregatesFilterSchema).optional(),
  validated: z.union([ z.lazy(() => BoolWithAggregatesFilterSchema),z.boolean() ]).optional(),
  createdAt: z.union([ z.lazy(() => DateTimeWithAggregatesFilterSchema),z.coerce.date() ]).optional(),
  updatedAt: z.union([ z.lazy(() => DateTimeWithAggregatesFilterSchema),z.coerce.date() ]).optional(),
  authorId: z.union([ z.lazy(() => StringWithAggregatesFilterSchema),z.string() ]).optional(),
}).strict();

export const NodeRelationshipWhereInputSchema: z.ZodType<Prisma.NodeRelationshipWhereInput> = z.object({
  AND: z.union([ z.lazy(() => NodeRelationshipWhereInputSchema),z.lazy(() => NodeRelationshipWhereInputSchema).array() ]).optional(),
  OR: z.lazy(() => NodeRelationshipWhereInputSchema).array().optional(),
  NOT: z.union([ z.lazy(() => NodeRelationshipWhereInputSchema),z.lazy(() => NodeRelationshipWhereInputSchema).array() ]).optional(),
  id: z.union([ z.lazy(() => StringFilterSchema),z.string() ]).optional(),
  fromId: z.union([ z.lazy(() => StringFilterSchema),z.string() ]).optional(),
  toId: z.union([ z.lazy(() => StringFilterSchema),z.string() ]).optional(),
  type: z.union([ z.lazy(() => EnumRelationshipTypeFilterSchema),z.lazy(() => RelationshipTypeSchema) ]).optional(),
  createdAt: z.union([ z.lazy(() => DateTimeFilterSchema),z.coerce.date() ]).optional(),
  updatedAt: z.union([ z.lazy(() => DateTimeFilterSchema),z.coerce.date() ]).optional(),
  from: z.union([ z.lazy(() => KnowledgeNodeScalarRelationFilterSchema),z.lazy(() => KnowledgeNodeWhereInputSchema) ]).optional(),
  to: z.union([ z.lazy(() => KnowledgeNodeScalarRelationFilterSchema),z.lazy(() => KnowledgeNodeWhereInputSchema) ]).optional(),
}).strict();

export const NodeRelationshipOrderByWithRelationInputSchema: z.ZodType<Prisma.NodeRelationshipOrderByWithRelationInput> = z.object({
  id: z.lazy(() => SortOrderSchema).optional(),
  fromId: z.lazy(() => SortOrderSchema).optional(),
  toId: z.lazy(() => SortOrderSchema).optional(),
  type: z.lazy(() => SortOrderSchema).optional(),
  createdAt: z.lazy(() => SortOrderSchema).optional(),
  updatedAt: z.lazy(() => SortOrderSchema).optional(),
  from: z.lazy(() => KnowledgeNodeOrderByWithRelationInputSchema).optional(),
  to: z.lazy(() => KnowledgeNodeOrderByWithRelationInputSchema).optional()
}).strict();

export const NodeRelationshipWhereUniqueInputSchema: z.ZodType<Prisma.NodeRelationshipWhereUniqueInput> = z.object({
  id: z.string().cuid()
})
.and(z.object({
  id: z.string().cuid().optional(),
  AND: z.union([ z.lazy(() => NodeRelationshipWhereInputSchema),z.lazy(() => NodeRelationshipWhereInputSchema).array() ]).optional(),
  OR: z.lazy(() => NodeRelationshipWhereInputSchema).array().optional(),
  NOT: z.union([ z.lazy(() => NodeRelationshipWhereInputSchema),z.lazy(() => NodeRelationshipWhereInputSchema).array() ]).optional(),
  fromId: z.union([ z.lazy(() => StringFilterSchema),z.string() ]).optional(),
  toId: z.union([ z.lazy(() => StringFilterSchema),z.string() ]).optional(),
  type: z.union([ z.lazy(() => EnumRelationshipTypeFilterSchema),z.lazy(() => RelationshipTypeSchema) ]).optional(),
  createdAt: z.union([ z.lazy(() => DateTimeFilterSchema),z.coerce.date() ]).optional(),
  updatedAt: z.union([ z.lazy(() => DateTimeFilterSchema),z.coerce.date() ]).optional(),
  from: z.union([ z.lazy(() => KnowledgeNodeScalarRelationFilterSchema),z.lazy(() => KnowledgeNodeWhereInputSchema) ]).optional(),
  to: z.union([ z.lazy(() => KnowledgeNodeScalarRelationFilterSchema),z.lazy(() => KnowledgeNodeWhereInputSchema) ]).optional(),
}).strict());

export const NodeRelationshipOrderByWithAggregationInputSchema: z.ZodType<Prisma.NodeRelationshipOrderByWithAggregationInput> = z.object({
  id: z.lazy(() => SortOrderSchema).optional(),
  fromId: z.lazy(() => SortOrderSchema).optional(),
  toId: z.lazy(() => SortOrderSchema).optional(),
  type: z.lazy(() => SortOrderSchema).optional(),
  createdAt: z.lazy(() => SortOrderSchema).optional(),
  updatedAt: z.lazy(() => SortOrderSchema).optional(),
  _count: z.lazy(() => NodeRelationshipCountOrderByAggregateInputSchema).optional(),
  _max: z.lazy(() => NodeRelationshipMaxOrderByAggregateInputSchema).optional(),
  _min: z.lazy(() => NodeRelationshipMinOrderByAggregateInputSchema).optional()
}).strict();

export const NodeRelationshipScalarWhereWithAggregatesInputSchema: z.ZodType<Prisma.NodeRelationshipScalarWhereWithAggregatesInput> = z.object({
  AND: z.union([ z.lazy(() => NodeRelationshipScalarWhereWithAggregatesInputSchema),z.lazy(() => NodeRelationshipScalarWhereWithAggregatesInputSchema).array() ]).optional(),
  OR: z.lazy(() => NodeRelationshipScalarWhereWithAggregatesInputSchema).array().optional(),
  NOT: z.union([ z.lazy(() => NodeRelationshipScalarWhereWithAggregatesInputSchema),z.lazy(() => NodeRelationshipScalarWhereWithAggregatesInputSchema).array() ]).optional(),
  id: z.union([ z.lazy(() => StringWithAggregatesFilterSchema),z.string() ]).optional(),
  fromId: z.union([ z.lazy(() => StringWithAggregatesFilterSchema),z.string() ]).optional(),
  toId: z.union([ z.lazy(() => StringWithAggregatesFilterSchema),z.string() ]).optional(),
  type: z.union([ z.lazy(() => EnumRelationshipTypeWithAggregatesFilterSchema),z.lazy(() => RelationshipTypeSchema) ]).optional(),
  createdAt: z.union([ z.lazy(() => DateTimeWithAggregatesFilterSchema),z.coerce.date() ]).optional(),
  updatedAt: z.union([ z.lazy(() => DateTimeWithAggregatesFilterSchema),z.coerce.date() ]).optional(),
}).strict();

export const ValidationWhereInputSchema: z.ZodType<Prisma.ValidationWhereInput> = z.object({
  AND: z.union([ z.lazy(() => ValidationWhereInputSchema),z.lazy(() => ValidationWhereInputSchema).array() ]).optional(),
  OR: z.lazy(() => ValidationWhereInputSchema).array().optional(),
  NOT: z.union([ z.lazy(() => ValidationWhereInputSchema),z.lazy(() => ValidationWhereInputSchema).array() ]).optional(),
  id: z.union([ z.lazy(() => StringFilterSchema),z.string() ]).optional(),
  nodeId: z.union([ z.lazy(() => StringFilterSchema),z.string() ]).optional(),
  expertId: z.union([ z.lazy(() => StringFilterSchema),z.string() ]).optional(),
  notes: z.union([ z.lazy(() => StringNullableFilterSchema),z.string() ]).optional().nullable(),
  createdAt: z.union([ z.lazy(() => DateTimeFilterSchema),z.coerce.date() ]).optional(),
  updatedAt: z.union([ z.lazy(() => DateTimeFilterSchema),z.coerce.date() ]).optional(),
  node: z.union([ z.lazy(() => KnowledgeNodeScalarRelationFilterSchema),z.lazy(() => KnowledgeNodeWhereInputSchema) ]).optional(),
  expert: z.union([ z.lazy(() => UserScalarRelationFilterSchema),z.lazy(() => UserWhereInputSchema) ]).optional(),
}).strict();

export const ValidationOrderByWithRelationInputSchema: z.ZodType<Prisma.ValidationOrderByWithRelationInput> = z.object({
  id: z.lazy(() => SortOrderSchema).optional(),
  nodeId: z.lazy(() => SortOrderSchema).optional(),
  expertId: z.lazy(() => SortOrderSchema).optional(),
  notes: z.union([ z.lazy(() => SortOrderSchema),z.lazy(() => SortOrderInputSchema) ]).optional(),
  createdAt: z.lazy(() => SortOrderSchema).optional(),
  updatedAt: z.lazy(() => SortOrderSchema).optional(),
  node: z.lazy(() => KnowledgeNodeOrderByWithRelationInputSchema).optional(),
  expert: z.lazy(() => UserOrderByWithRelationInputSchema).optional()
}).strict();

export const ValidationWhereUniqueInputSchema: z.ZodType<Prisma.ValidationWhereUniqueInput> = z.object({
  id: z.string().cuid()
})
.and(z.object({
  id: z.string().cuid().optional(),
  AND: z.union([ z.lazy(() => ValidationWhereInputSchema),z.lazy(() => ValidationWhereInputSchema).array() ]).optional(),
  OR: z.lazy(() => ValidationWhereInputSchema).array().optional(),
  NOT: z.union([ z.lazy(() => ValidationWhereInputSchema),z.lazy(() => ValidationWhereInputSchema).array() ]).optional(),
  nodeId: z.union([ z.lazy(() => StringFilterSchema),z.string() ]).optional(),
  expertId: z.union([ z.lazy(() => StringFilterSchema),z.string() ]).optional(),
  notes: z.union([ z.lazy(() => StringNullableFilterSchema),z.string() ]).optional().nullable(),
  createdAt: z.union([ z.lazy(() => DateTimeFilterSchema),z.coerce.date() ]).optional(),
  updatedAt: z.union([ z.lazy(() => DateTimeFilterSchema),z.coerce.date() ]).optional(),
  node: z.union([ z.lazy(() => KnowledgeNodeScalarRelationFilterSchema),z.lazy(() => KnowledgeNodeWhereInputSchema) ]).optional(),
  expert: z.union([ z.lazy(() => UserScalarRelationFilterSchema),z.lazy(() => UserWhereInputSchema) ]).optional(),
}).strict());

export const ValidationOrderByWithAggregationInputSchema: z.ZodType<Prisma.ValidationOrderByWithAggregationInput> = z.object({
  id: z.lazy(() => SortOrderSchema).optional(),
  nodeId: z.lazy(() => SortOrderSchema).optional(),
  expertId: z.lazy(() => SortOrderSchema).optional(),
  notes: z.union([ z.lazy(() => SortOrderSchema),z.lazy(() => SortOrderInputSchema) ]).optional(),
  createdAt: z.lazy(() => SortOrderSchema).optional(),
  updatedAt: z.lazy(() => SortOrderSchema).optional(),
  _count: z.lazy(() => ValidationCountOrderByAggregateInputSchema).optional(),
  _max: z.lazy(() => ValidationMaxOrderByAggregateInputSchema).optional(),
  _min: z.lazy(() => ValidationMinOrderByAggregateInputSchema).optional()
}).strict();

export const ValidationScalarWhereWithAggregatesInputSchema: z.ZodType<Prisma.ValidationScalarWhereWithAggregatesInput> = z.object({
  AND: z.union([ z.lazy(() => ValidationScalarWhereWithAggregatesInputSchema),z.lazy(() => ValidationScalarWhereWithAggregatesInputSchema).array() ]).optional(),
  OR: z.lazy(() => ValidationScalarWhereWithAggregatesInputSchema).array().optional(),
  NOT: z.union([ z.lazy(() => ValidationScalarWhereWithAggregatesInputSchema),z.lazy(() => ValidationScalarWhereWithAggregatesInputSchema).array() ]).optional(),
  id: z.union([ z.lazy(() => StringWithAggregatesFilterSchema),z.string() ]).optional(),
  nodeId: z.union([ z.lazy(() => StringWithAggregatesFilterSchema),z.string() ]).optional(),
  expertId: z.union([ z.lazy(() => StringWithAggregatesFilterSchema),z.string() ]).optional(),
  notes: z.union([ z.lazy(() => StringNullableWithAggregatesFilterSchema),z.string() ]).optional().nullable(),
  createdAt: z.union([ z.lazy(() => DateTimeWithAggregatesFilterSchema),z.coerce.date() ]).optional(),
  updatedAt: z.union([ z.lazy(() => DateTimeWithAggregatesFilterSchema),z.coerce.date() ]).optional(),
}).strict();

export const TopicWhereInputSchema: z.ZodType<Prisma.TopicWhereInput> = z.object({
  AND: z.union([ z.lazy(() => TopicWhereInputSchema),z.lazy(() => TopicWhereInputSchema).array() ]).optional(),
  OR: z.lazy(() => TopicWhereInputSchema).array().optional(),
  NOT: z.union([ z.lazy(() => TopicWhereInputSchema),z.lazy(() => TopicWhereInputSchema).array() ]).optional(),
  id: z.union([ z.lazy(() => StringFilterSchema),z.string() ]).optional(),
  name: z.union([ z.lazy(() => StringFilterSchema),z.string() ]).optional(),
  createdAt: z.union([ z.lazy(() => DateTimeFilterSchema),z.coerce.date() ]).optional(),
  updatedAt: z.union([ z.lazy(() => DateTimeFilterSchema),z.coerce.date() ]).optional(),
  nodes: z.lazy(() => KnowledgeNodeListRelationFilterSchema).optional()
}).strict();

export const TopicOrderByWithRelationInputSchema: z.ZodType<Prisma.TopicOrderByWithRelationInput> = z.object({
  id: z.lazy(() => SortOrderSchema).optional(),
  name: z.lazy(() => SortOrderSchema).optional(),
  createdAt: z.lazy(() => SortOrderSchema).optional(),
  updatedAt: z.lazy(() => SortOrderSchema).optional(),
  nodes: z.lazy(() => KnowledgeNodeOrderByRelationAggregateInputSchema).optional()
}).strict();

export const TopicWhereUniqueInputSchema: z.ZodType<Prisma.TopicWhereUniqueInput> = z.union([
  z.object({
    id: z.string().cuid(),
    name: z.string()
  }),
  z.object({
    id: z.string().cuid(),
  }),
  z.object({
    name: z.string(),
  }),
])
.and(z.object({
  id: z.string().cuid().optional(),
  name: z.string().optional(),
  AND: z.union([ z.lazy(() => TopicWhereInputSchema),z.lazy(() => TopicWhereInputSchema).array() ]).optional(),
  OR: z.lazy(() => TopicWhereInputSchema).array().optional(),
  NOT: z.union([ z.lazy(() => TopicWhereInputSchema),z.lazy(() => TopicWhereInputSchema).array() ]).optional(),
  createdAt: z.union([ z.lazy(() => DateTimeFilterSchema),z.coerce.date() ]).optional(),
  updatedAt: z.union([ z.lazy(() => DateTimeFilterSchema),z.coerce.date() ]).optional(),
  nodes: z.lazy(() => KnowledgeNodeListRelationFilterSchema).optional()
}).strict());

export const TopicOrderByWithAggregationInputSchema: z.ZodType<Prisma.TopicOrderByWithAggregationInput> = z.object({
  id: z.lazy(() => SortOrderSchema).optional(),
  name: z.lazy(() => SortOrderSchema).optional(),
  createdAt: z.lazy(() => SortOrderSchema).optional(),
  updatedAt: z.lazy(() => SortOrderSchema).optional(),
  _count: z.lazy(() => TopicCountOrderByAggregateInputSchema).optional(),
  _max: z.lazy(() => TopicMaxOrderByAggregateInputSchema).optional(),
  _min: z.lazy(() => TopicMinOrderByAggregateInputSchema).optional()
}).strict();

export const TopicScalarWhereWithAggregatesInputSchema: z.ZodType<Prisma.TopicScalarWhereWithAggregatesInput> = z.object({
  AND: z.union([ z.lazy(() => TopicScalarWhereWithAggregatesInputSchema),z.lazy(() => TopicScalarWhereWithAggregatesInputSchema).array() ]).optional(),
  OR: z.lazy(() => TopicScalarWhereWithAggregatesInputSchema).array().optional(),
  NOT: z.union([ z.lazy(() => TopicScalarWhereWithAggregatesInputSchema),z.lazy(() => TopicScalarWhereWithAggregatesInputSchema).array() ]).optional(),
  id: z.union([ z.lazy(() => StringWithAggregatesFilterSchema),z.string() ]).optional(),
  name: z.union([ z.lazy(() => StringWithAggregatesFilterSchema),z.string() ]).optional(),
  createdAt: z.union([ z.lazy(() => DateTimeWithAggregatesFilterSchema),z.coerce.date() ]).optional(),
  updatedAt: z.union([ z.lazy(() => DateTimeWithAggregatesFilterSchema),z.coerce.date() ]).optional(),
}).strict();

export const FooCreateInputSchema: z.ZodType<Prisma.FooCreateInput> = z.object({
  name: z.string(),
  email: z.string()
}).strict();

export const FooUncheckedCreateInputSchema: z.ZodType<Prisma.FooUncheckedCreateInput> = z.object({
  id: z.number().int().optional(),
  name: z.string(),
  email: z.string()
}).strict();

export const FooUpdateInputSchema: z.ZodType<Prisma.FooUpdateInput> = z.object({
  name: z.union([ z.string(),z.lazy(() => StringFieldUpdateOperationsInputSchema) ]).optional(),
  email: z.union([ z.string(),z.lazy(() => StringFieldUpdateOperationsInputSchema) ]).optional(),
}).strict();

export const FooUncheckedUpdateInputSchema: z.ZodType<Prisma.FooUncheckedUpdateInput> = z.object({
  id: z.union([ z.number().int(),z.lazy(() => IntFieldUpdateOperationsInputSchema) ]).optional(),
  name: z.union([ z.string(),z.lazy(() => StringFieldUpdateOperationsInputSchema) ]).optional(),
  email: z.union([ z.string(),z.lazy(() => StringFieldUpdateOperationsInputSchema) ]).optional(),
}).strict();

export const FooCreateManyInputSchema: z.ZodType<Prisma.FooCreateManyInput> = z.object({
  id: z.number().int().optional(),
  name: z.string(),
  email: z.string()
}).strict();

export const FooUpdateManyMutationInputSchema: z.ZodType<Prisma.FooUpdateManyMutationInput> = z.object({
  name: z.union([ z.string(),z.lazy(() => StringFieldUpdateOperationsInputSchema) ]).optional(),
  email: z.union([ z.string(),z.lazy(() => StringFieldUpdateOperationsInputSchema) ]).optional(),
}).strict();

export const FooUncheckedUpdateManyInputSchema: z.ZodType<Prisma.FooUncheckedUpdateManyInput> = z.object({
  id: z.union([ z.number().int(),z.lazy(() => IntFieldUpdateOperationsInputSchema) ]).optional(),
  name: z.union([ z.string(),z.lazy(() => StringFieldUpdateOperationsInputSchema) ]).optional(),
  email: z.union([ z.string(),z.lazy(() => StringFieldUpdateOperationsInputSchema) ]).optional(),
}).strict();

export const TempEmbeddingsTableUpdateInputSchema: z.ZodType<Prisma.TempEmbeddingsTableUpdateInput> = z.object({
  id: z.union([ z.string().cuid(),z.lazy(() => StringFieldUpdateOperationsInputSchema) ]).optional(),
  parentId: z.union([ z.string(),z.lazy(() => NullableStringFieldUpdateOperationsInputSchema) ]).optional().nullable(),
  type: z.union([ z.string(),z.lazy(() => NullableStringFieldUpdateOperationsInputSchema) ]).optional().nullable(),
  text: z.union([ z.string(),z.lazy(() => NullableStringFieldUpdateOperationsInputSchema) ]).optional().nullable(),
  metadata: z.union([ z.lazy(() => NullableJsonNullValueInputSchema),InputJsonValueSchema ]).optional(),
  createdAt: z.union([ z.coerce.date(),z.lazy(() => DateTimeFieldUpdateOperationsInputSchema) ]).optional(),
  updatedAt: z.union([ z.coerce.date(),z.lazy(() => DateTimeFieldUpdateOperationsInputSchema) ]).optional(),
}).strict();

export const TempEmbeddingsTableUncheckedUpdateInputSchema: z.ZodType<Prisma.TempEmbeddingsTableUncheckedUpdateInput> = z.object({
  id: z.union([ z.string().cuid(),z.lazy(() => StringFieldUpdateOperationsInputSchema) ]).optional(),
  parentId: z.union([ z.string(),z.lazy(() => NullableStringFieldUpdateOperationsInputSchema) ]).optional().nullable(),
  type: z.union([ z.string(),z.lazy(() => NullableStringFieldUpdateOperationsInputSchema) ]).optional().nullable(),
  text: z.union([ z.string(),z.lazy(() => NullableStringFieldUpdateOperationsInputSchema) ]).optional().nullable(),
  metadata: z.union([ z.lazy(() => NullableJsonNullValueInputSchema),InputJsonValueSchema ]).optional(),
  createdAt: z.union([ z.coerce.date(),z.lazy(() => DateTimeFieldUpdateOperationsInputSchema) ]).optional(),
  updatedAt: z.union([ z.coerce.date(),z.lazy(() => DateTimeFieldUpdateOperationsInputSchema) ]).optional(),
}).strict();

export const TempEmbeddingsTableUpdateManyMutationInputSchema: z.ZodType<Prisma.TempEmbeddingsTableUpdateManyMutationInput> = z.object({
  id: z.union([ z.string().cuid(),z.lazy(() => StringFieldUpdateOperationsInputSchema) ]).optional(),
  parentId: z.union([ z.string(),z.lazy(() => NullableStringFieldUpdateOperationsInputSchema) ]).optional().nullable(),
  type: z.union([ z.string(),z.lazy(() => NullableStringFieldUpdateOperationsInputSchema) ]).optional().nullable(),
  text: z.union([ z.string(),z.lazy(() => NullableStringFieldUpdateOperationsInputSchema) ]).optional().nullable(),
  metadata: z.union([ z.lazy(() => NullableJsonNullValueInputSchema),InputJsonValueSchema ]).optional(),
  createdAt: z.union([ z.coerce.date(),z.lazy(() => DateTimeFieldUpdateOperationsInputSchema) ]).optional(),
  updatedAt: z.union([ z.coerce.date(),z.lazy(() => DateTimeFieldUpdateOperationsInputSchema) ]).optional(),
}).strict();

export const TempEmbeddingsTableUncheckedUpdateManyInputSchema: z.ZodType<Prisma.TempEmbeddingsTableUncheckedUpdateManyInput> = z.object({
  id: z.union([ z.string().cuid(),z.lazy(() => StringFieldUpdateOperationsInputSchema) ]).optional(),
  parentId: z.union([ z.string(),z.lazy(() => NullableStringFieldUpdateOperationsInputSchema) ]).optional().nullable(),
  type: z.union([ z.string(),z.lazy(() => NullableStringFieldUpdateOperationsInputSchema) ]).optional().nullable(),
  text: z.union([ z.string(),z.lazy(() => NullableStringFieldUpdateOperationsInputSchema) ]).optional().nullable(),
  metadata: z.union([ z.lazy(() => NullableJsonNullValueInputSchema),InputJsonValueSchema ]).optional(),
  createdAt: z.union([ z.coerce.date(),z.lazy(() => DateTimeFieldUpdateOperationsInputSchema) ]).optional(),
  updatedAt: z.union([ z.coerce.date(),z.lazy(() => DateTimeFieldUpdateOperationsInputSchema) ]).optional(),
}).strict();

export const UserCreateInputSchema: z.ZodType<Prisma.UserCreateInput> = z.object({
  userId: z.string(),
  nodes: z.lazy(() => KnowledgeNodeCreateNestedManyWithoutAuthorInputSchema).optional(),
  validations: z.lazy(() => ValidationCreateNestedManyWithoutExpertInputSchema).optional()
}).strict();

export const UserUncheckedCreateInputSchema: z.ZodType<Prisma.UserUncheckedCreateInput> = z.object({
  userId: z.string(),
  nodes: z.lazy(() => KnowledgeNodeUncheckedCreateNestedManyWithoutAuthorInputSchema).optional(),
  validations: z.lazy(() => ValidationUncheckedCreateNestedManyWithoutExpertInputSchema).optional()
}).strict();

export const UserUpdateInputSchema: z.ZodType<Prisma.UserUpdateInput> = z.object({
  userId: z.union([ z.string(),z.lazy(() => StringFieldUpdateOperationsInputSchema) ]).optional(),
  nodes: z.lazy(() => KnowledgeNodeUpdateManyWithoutAuthorNestedInputSchema).optional(),
  validations: z.lazy(() => ValidationUpdateManyWithoutExpertNestedInputSchema).optional()
}).strict();

export const UserUncheckedUpdateInputSchema: z.ZodType<Prisma.UserUncheckedUpdateInput> = z.object({
  userId: z.union([ z.string(),z.lazy(() => StringFieldUpdateOperationsInputSchema) ]).optional(),
  nodes: z.lazy(() => KnowledgeNodeUncheckedUpdateManyWithoutAuthorNestedInputSchema).optional(),
  validations: z.lazy(() => ValidationUncheckedUpdateManyWithoutExpertNestedInputSchema).optional()
}).strict();

export const UserCreateManyInputSchema: z.ZodType<Prisma.UserCreateManyInput> = z.object({
  userId: z.string()
}).strict();

export const UserUpdateManyMutationInputSchema: z.ZodType<Prisma.UserUpdateManyMutationInput> = z.object({
  userId: z.union([ z.string(),z.lazy(() => StringFieldUpdateOperationsInputSchema) ]).optional(),
}).strict();

export const UserUncheckedUpdateManyInputSchema: z.ZodType<Prisma.UserUncheckedUpdateManyInput> = z.object({
  userId: z.union([ z.string(),z.lazy(() => StringFieldUpdateOperationsInputSchema) ]).optional(),
}).strict();

export const KnowledgeNodeCreateInputSchema: z.ZodType<Prisma.KnowledgeNodeCreateInput> = z.object({
  id: z.string().cuid().optional(),
  content: z.string(),
  embedding: z.union([ z.lazy(() => KnowledgeNodeCreateembeddingInputSchema),z.number().array() ]).optional(),
  type: z.lazy(() => NodeTypeSchema),
  source: z.lazy(() => SourceTypeSchema),
  context: z.union([ z.lazy(() => NullableJsonNullValueInputSchema),InputJsonValueSchema ]).optional(),
  validated: z.boolean().optional(),
  createdAt: z.coerce.date().optional(),
  updatedAt: z.coerce.date().optional(),
  author: z.lazy(() => UserCreateNestedOneWithoutNodesInputSchema),
  relationshipsFrom: z.lazy(() => NodeRelationshipCreateNestedManyWithoutFromInputSchema).optional(),
  relationshipsTo: z.lazy(() => NodeRelationshipCreateNestedManyWithoutToInputSchema).optional(),
  validations: z.lazy(() => ValidationCreateNestedManyWithoutNodeInputSchema).optional(),
  topics: z.lazy(() => TopicCreateNestedManyWithoutNodesInputSchema).optional()
}).strict();

export const KnowledgeNodeUncheckedCreateInputSchema: z.ZodType<Prisma.KnowledgeNodeUncheckedCreateInput> = z.object({
  id: z.string().cuid().optional(),
  content: z.string(),
  embedding: z.union([ z.lazy(() => KnowledgeNodeCreateembeddingInputSchema),z.number().array() ]).optional(),
  type: z.lazy(() => NodeTypeSchema),
  source: z.lazy(() => SourceTypeSchema),
  context: z.union([ z.lazy(() => NullableJsonNullValueInputSchema),InputJsonValueSchema ]).optional(),
  validated: z.boolean().optional(),
  createdAt: z.coerce.date().optional(),
  updatedAt: z.coerce.date().optional(),
  authorId: z.string(),
  relationshipsFrom: z.lazy(() => NodeRelationshipUncheckedCreateNestedManyWithoutFromInputSchema).optional(),
  relationshipsTo: z.lazy(() => NodeRelationshipUncheckedCreateNestedManyWithoutToInputSchema).optional(),
  validations: z.lazy(() => ValidationUncheckedCreateNestedManyWithoutNodeInputSchema).optional(),
  topics: z.lazy(() => TopicUncheckedCreateNestedManyWithoutNodesInputSchema).optional()
}).strict();

export const KnowledgeNodeUpdateInputSchema: z.ZodType<Prisma.KnowledgeNodeUpdateInput> = z.object({
  id: z.union([ z.string().cuid(),z.lazy(() => StringFieldUpdateOperationsInputSchema) ]).optional(),
  content: z.union([ z.string(),z.lazy(() => StringFieldUpdateOperationsInputSchema) ]).optional(),
  embedding: z.union([ z.lazy(() => KnowledgeNodeUpdateembeddingInputSchema),z.number().array() ]).optional(),
  type: z.union([ z.lazy(() => NodeTypeSchema),z.lazy(() => EnumNodeTypeFieldUpdateOperationsInputSchema) ]).optional(),
  source: z.union([ z.lazy(() => SourceTypeSchema),z.lazy(() => EnumSourceTypeFieldUpdateOperationsInputSchema) ]).optional(),
  context: z.union([ z.lazy(() => NullableJsonNullValueInputSchema),InputJsonValueSchema ]).optional(),
  validated: z.union([ z.boolean(),z.lazy(() => BoolFieldUpdateOperationsInputSchema) ]).optional(),
  createdAt: z.union([ z.coerce.date(),z.lazy(() => DateTimeFieldUpdateOperationsInputSchema) ]).optional(),
  updatedAt: z.union([ z.coerce.date(),z.lazy(() => DateTimeFieldUpdateOperationsInputSchema) ]).optional(),
  author: z.lazy(() => UserUpdateOneRequiredWithoutNodesNestedInputSchema).optional(),
  relationshipsFrom: z.lazy(() => NodeRelationshipUpdateManyWithoutFromNestedInputSchema).optional(),
  relationshipsTo: z.lazy(() => NodeRelationshipUpdateManyWithoutToNestedInputSchema).optional(),
  validations: z.lazy(() => ValidationUpdateManyWithoutNodeNestedInputSchema).optional(),
  topics: z.lazy(() => TopicUpdateManyWithoutNodesNestedInputSchema).optional()
}).strict();

export const KnowledgeNodeUncheckedUpdateInputSchema: z.ZodType<Prisma.KnowledgeNodeUncheckedUpdateInput> = z.object({
  id: z.union([ z.string().cuid(),z.lazy(() => StringFieldUpdateOperationsInputSchema) ]).optional(),
  content: z.union([ z.string(),z.lazy(() => StringFieldUpdateOperationsInputSchema) ]).optional(),
  embedding: z.union([ z.lazy(() => KnowledgeNodeUpdateembeddingInputSchema),z.number().array() ]).optional(),
  type: z.union([ z.lazy(() => NodeTypeSchema),z.lazy(() => EnumNodeTypeFieldUpdateOperationsInputSchema) ]).optional(),
  source: z.union([ z.lazy(() => SourceTypeSchema),z.lazy(() => EnumSourceTypeFieldUpdateOperationsInputSchema) ]).optional(),
  context: z.union([ z.lazy(() => NullableJsonNullValueInputSchema),InputJsonValueSchema ]).optional(),
  validated: z.union([ z.boolean(),z.lazy(() => BoolFieldUpdateOperationsInputSchema) ]).optional(),
  createdAt: z.union([ z.coerce.date(),z.lazy(() => DateTimeFieldUpdateOperationsInputSchema) ]).optional(),
  updatedAt: z.union([ z.coerce.date(),z.lazy(() => DateTimeFieldUpdateOperationsInputSchema) ]).optional(),
  authorId: z.union([ z.string(),z.lazy(() => StringFieldUpdateOperationsInputSchema) ]).optional(),
  relationshipsFrom: z.lazy(() => NodeRelationshipUncheckedUpdateManyWithoutFromNestedInputSchema).optional(),
  relationshipsTo: z.lazy(() => NodeRelationshipUncheckedUpdateManyWithoutToNestedInputSchema).optional(),
  validations: z.lazy(() => ValidationUncheckedUpdateManyWithoutNodeNestedInputSchema).optional(),
  topics: z.lazy(() => TopicUncheckedUpdateManyWithoutNodesNestedInputSchema).optional()
}).strict();

export const KnowledgeNodeCreateManyInputSchema: z.ZodType<Prisma.KnowledgeNodeCreateManyInput> = z.object({
  id: z.string().cuid().optional(),
  content: z.string(),
  embedding: z.union([ z.lazy(() => KnowledgeNodeCreateembeddingInputSchema),z.number().array() ]).optional(),
  type: z.lazy(() => NodeTypeSchema),
  source: z.lazy(() => SourceTypeSchema),
  context: z.union([ z.lazy(() => NullableJsonNullValueInputSchema),InputJsonValueSchema ]).optional(),
  validated: z.boolean().optional(),
  createdAt: z.coerce.date().optional(),
  updatedAt: z.coerce.date().optional(),
  authorId: z.string()
}).strict();

export const KnowledgeNodeUpdateManyMutationInputSchema: z.ZodType<Prisma.KnowledgeNodeUpdateManyMutationInput> = z.object({
  id: z.union([ z.string().cuid(),z.lazy(() => StringFieldUpdateOperationsInputSchema) ]).optional(),
  content: z.union([ z.string(),z.lazy(() => StringFieldUpdateOperationsInputSchema) ]).optional(),
  embedding: z.union([ z.lazy(() => KnowledgeNodeUpdateembeddingInputSchema),z.number().array() ]).optional(),
  type: z.union([ z.lazy(() => NodeTypeSchema),z.lazy(() => EnumNodeTypeFieldUpdateOperationsInputSchema) ]).optional(),
  source: z.union([ z.lazy(() => SourceTypeSchema),z.lazy(() => EnumSourceTypeFieldUpdateOperationsInputSchema) ]).optional(),
  context: z.union([ z.lazy(() => NullableJsonNullValueInputSchema),InputJsonValueSchema ]).optional(),
  validated: z.union([ z.boolean(),z.lazy(() => BoolFieldUpdateOperationsInputSchema) ]).optional(),
  createdAt: z.union([ z.coerce.date(),z.lazy(() => DateTimeFieldUpdateOperationsInputSchema) ]).optional(),
  updatedAt: z.union([ z.coerce.date(),z.lazy(() => DateTimeFieldUpdateOperationsInputSchema) ]).optional(),
}).strict();

export const KnowledgeNodeUncheckedUpdateManyInputSchema: z.ZodType<Prisma.KnowledgeNodeUncheckedUpdateManyInput> = z.object({
  id: z.union([ z.string().cuid(),z.lazy(() => StringFieldUpdateOperationsInputSchema) ]).optional(),
  content: z.union([ z.string(),z.lazy(() => StringFieldUpdateOperationsInputSchema) ]).optional(),
  embedding: z.union([ z.lazy(() => KnowledgeNodeUpdateembeddingInputSchema),z.number().array() ]).optional(),
  type: z.union([ z.lazy(() => NodeTypeSchema),z.lazy(() => EnumNodeTypeFieldUpdateOperationsInputSchema) ]).optional(),
  source: z.union([ z.lazy(() => SourceTypeSchema),z.lazy(() => EnumSourceTypeFieldUpdateOperationsInputSchema) ]).optional(),
  context: z.union([ z.lazy(() => NullableJsonNullValueInputSchema),InputJsonValueSchema ]).optional(),
  validated: z.union([ z.boolean(),z.lazy(() => BoolFieldUpdateOperationsInputSchema) ]).optional(),
  createdAt: z.union([ z.coerce.date(),z.lazy(() => DateTimeFieldUpdateOperationsInputSchema) ]).optional(),
  updatedAt: z.union([ z.coerce.date(),z.lazy(() => DateTimeFieldUpdateOperationsInputSchema) ]).optional(),
  authorId: z.union([ z.string(),z.lazy(() => StringFieldUpdateOperationsInputSchema) ]).optional(),
}).strict();

export const NodeRelationshipCreateInputSchema: z.ZodType<Prisma.NodeRelationshipCreateInput> = z.object({
  id: z.string().cuid().optional(),
  type: z.lazy(() => RelationshipTypeSchema),
  createdAt: z.coerce.date().optional(),
  updatedAt: z.coerce.date().optional(),
  from: z.lazy(() => KnowledgeNodeCreateNestedOneWithoutRelationshipsFromInputSchema),
  to: z.lazy(() => KnowledgeNodeCreateNestedOneWithoutRelationshipsToInputSchema)
}).strict();

export const NodeRelationshipUncheckedCreateInputSchema: z.ZodType<Prisma.NodeRelationshipUncheckedCreateInput> = z.object({
  id: z.string().cuid().optional(),
  fromId: z.string(),
  toId: z.string(),
  type: z.lazy(() => RelationshipTypeSchema),
  createdAt: z.coerce.date().optional(),
  updatedAt: z.coerce.date().optional()
}).strict();

export const NodeRelationshipUpdateInputSchema: z.ZodType<Prisma.NodeRelationshipUpdateInput> = z.object({
  id: z.union([ z.string().cuid(),z.lazy(() => StringFieldUpdateOperationsInputSchema) ]).optional(),
  type: z.union([ z.lazy(() => RelationshipTypeSchema),z.lazy(() => EnumRelationshipTypeFieldUpdateOperationsInputSchema) ]).optional(),
  createdAt: z.union([ z.coerce.date(),z.lazy(() => DateTimeFieldUpdateOperationsInputSchema) ]).optional(),
  updatedAt: z.union([ z.coerce.date(),z.lazy(() => DateTimeFieldUpdateOperationsInputSchema) ]).optional(),
  from: z.lazy(() => KnowledgeNodeUpdateOneRequiredWithoutRelationshipsFromNestedInputSchema).optional(),
  to: z.lazy(() => KnowledgeNodeUpdateOneRequiredWithoutRelationshipsToNestedInputSchema).optional()
}).strict();

export const NodeRelationshipUncheckedUpdateInputSchema: z.ZodType<Prisma.NodeRelationshipUncheckedUpdateInput> = z.object({
  id: z.union([ z.string().cuid(),z.lazy(() => StringFieldUpdateOperationsInputSchema) ]).optional(),
  fromId: z.union([ z.string(),z.lazy(() => StringFieldUpdateOperationsInputSchema) ]).optional(),
  toId: z.union([ z.string(),z.lazy(() => StringFieldUpdateOperationsInputSchema) ]).optional(),
  type: z.union([ z.lazy(() => RelationshipTypeSchema),z.lazy(() => EnumRelationshipTypeFieldUpdateOperationsInputSchema) ]).optional(),
  createdAt: z.union([ z.coerce.date(),z.lazy(() => DateTimeFieldUpdateOperationsInputSchema) ]).optional(),
  updatedAt: z.union([ z.coerce.date(),z.lazy(() => DateTimeFieldUpdateOperationsInputSchema) ]).optional(),
}).strict();

export const NodeRelationshipCreateManyInputSchema: z.ZodType<Prisma.NodeRelationshipCreateManyInput> = z.object({
  id: z.string().cuid().optional(),
  fromId: z.string(),
  toId: z.string(),
  type: z.lazy(() => RelationshipTypeSchema),
  createdAt: z.coerce.date().optional(),
  updatedAt: z.coerce.date().optional()
}).strict();

export const NodeRelationshipUpdateManyMutationInputSchema: z.ZodType<Prisma.NodeRelationshipUpdateManyMutationInput> = z.object({
  id: z.union([ z.string().cuid(),z.lazy(() => StringFieldUpdateOperationsInputSchema) ]).optional(),
  type: z.union([ z.lazy(() => RelationshipTypeSchema),z.lazy(() => EnumRelationshipTypeFieldUpdateOperationsInputSchema) ]).optional(),
  createdAt: z.union([ z.coerce.date(),z.lazy(() => DateTimeFieldUpdateOperationsInputSchema) ]).optional(),
  updatedAt: z.union([ z.coerce.date(),z.lazy(() => DateTimeFieldUpdateOperationsInputSchema) ]).optional(),
}).strict();

export const NodeRelationshipUncheckedUpdateManyInputSchema: z.ZodType<Prisma.NodeRelationshipUncheckedUpdateManyInput> = z.object({
  id: z.union([ z.string().cuid(),z.lazy(() => StringFieldUpdateOperationsInputSchema) ]).optional(),
  fromId: z.union([ z.string(),z.lazy(() => StringFieldUpdateOperationsInputSchema) ]).optional(),
  toId: z.union([ z.string(),z.lazy(() => StringFieldUpdateOperationsInputSchema) ]).optional(),
  type: z.union([ z.lazy(() => RelationshipTypeSchema),z.lazy(() => EnumRelationshipTypeFieldUpdateOperationsInputSchema) ]).optional(),
  createdAt: z.union([ z.coerce.date(),z.lazy(() => DateTimeFieldUpdateOperationsInputSchema) ]).optional(),
  updatedAt: z.union([ z.coerce.date(),z.lazy(() => DateTimeFieldUpdateOperationsInputSchema) ]).optional(),
}).strict();

export const ValidationCreateInputSchema: z.ZodType<Prisma.ValidationCreateInput> = z.object({
  id: z.string().cuid().optional(),
  notes: z.string().optional().nullable(),
  createdAt: z.coerce.date().optional(),
  updatedAt: z.coerce.date().optional(),
  node: z.lazy(() => KnowledgeNodeCreateNestedOneWithoutValidationsInputSchema),
  expert: z.lazy(() => UserCreateNestedOneWithoutValidationsInputSchema)
}).strict();

export const ValidationUncheckedCreateInputSchema: z.ZodType<Prisma.ValidationUncheckedCreateInput> = z.object({
  id: z.string().cuid().optional(),
  nodeId: z.string(),
  expertId: z.string(),
  notes: z.string().optional().nullable(),
  createdAt: z.coerce.date().optional(),
  updatedAt: z.coerce.date().optional()
}).strict();

export const ValidationUpdateInputSchema: z.ZodType<Prisma.ValidationUpdateInput> = z.object({
  id: z.union([ z.string().cuid(),z.lazy(() => StringFieldUpdateOperationsInputSchema) ]).optional(),
  notes: z.union([ z.string(),z.lazy(() => NullableStringFieldUpdateOperationsInputSchema) ]).optional().nullable(),
  createdAt: z.union([ z.coerce.date(),z.lazy(() => DateTimeFieldUpdateOperationsInputSchema) ]).optional(),
  updatedAt: z.union([ z.coerce.date(),z.lazy(() => DateTimeFieldUpdateOperationsInputSchema) ]).optional(),
  node: z.lazy(() => KnowledgeNodeUpdateOneRequiredWithoutValidationsNestedInputSchema).optional(),
  expert: z.lazy(() => UserUpdateOneRequiredWithoutValidationsNestedInputSchema).optional()
}).strict();

export const ValidationUncheckedUpdateInputSchema: z.ZodType<Prisma.ValidationUncheckedUpdateInput> = z.object({
  id: z.union([ z.string().cuid(),z.lazy(() => StringFieldUpdateOperationsInputSchema) ]).optional(),
  nodeId: z.union([ z.string(),z.lazy(() => StringFieldUpdateOperationsInputSchema) ]).optional(),
  expertId: z.union([ z.string(),z.lazy(() => StringFieldUpdateOperationsInputSchema) ]).optional(),
  notes: z.union([ z.string(),z.lazy(() => NullableStringFieldUpdateOperationsInputSchema) ]).optional().nullable(),
  createdAt: z.union([ z.coerce.date(),z.lazy(() => DateTimeFieldUpdateOperationsInputSchema) ]).optional(),
  updatedAt: z.union([ z.coerce.date(),z.lazy(() => DateTimeFieldUpdateOperationsInputSchema) ]).optional(),
}).strict();

export const ValidationCreateManyInputSchema: z.ZodType<Prisma.ValidationCreateManyInput> = z.object({
  id: z.string().cuid().optional(),
  nodeId: z.string(),
  expertId: z.string(),
  notes: z.string().optional().nullable(),
  createdAt: z.coerce.date().optional(),
  updatedAt: z.coerce.date().optional()
}).strict();

export const ValidationUpdateManyMutationInputSchema: z.ZodType<Prisma.ValidationUpdateManyMutationInput> = z.object({
  id: z.union([ z.string().cuid(),z.lazy(() => StringFieldUpdateOperationsInputSchema) ]).optional(),
  notes: z.union([ z.string(),z.lazy(() => NullableStringFieldUpdateOperationsInputSchema) ]).optional().nullable(),
  createdAt: z.union([ z.coerce.date(),z.lazy(() => DateTimeFieldUpdateOperationsInputSchema) ]).optional(),
  updatedAt: z.union([ z.coerce.date(),z.lazy(() => DateTimeFieldUpdateOperationsInputSchema) ]).optional(),
}).strict();

export const ValidationUncheckedUpdateManyInputSchema: z.ZodType<Prisma.ValidationUncheckedUpdateManyInput> = z.object({
  id: z.union([ z.string().cuid(),z.lazy(() => StringFieldUpdateOperationsInputSchema) ]).optional(),
  nodeId: z.union([ z.string(),z.lazy(() => StringFieldUpdateOperationsInputSchema) ]).optional(),
  expertId: z.union([ z.string(),z.lazy(() => StringFieldUpdateOperationsInputSchema) ]).optional(),
  notes: z.union([ z.string(),z.lazy(() => NullableStringFieldUpdateOperationsInputSchema) ]).optional().nullable(),
  createdAt: z.union([ z.coerce.date(),z.lazy(() => DateTimeFieldUpdateOperationsInputSchema) ]).optional(),
  updatedAt: z.union([ z.coerce.date(),z.lazy(() => DateTimeFieldUpdateOperationsInputSchema) ]).optional(),
}).strict();

export const TopicCreateInputSchema: z.ZodType<Prisma.TopicCreateInput> = z.object({
  id: z.string().cuid().optional(),
  name: z.string(),
  createdAt: z.coerce.date().optional(),
  updatedAt: z.coerce.date().optional(),
  nodes: z.lazy(() => KnowledgeNodeCreateNestedManyWithoutTopicsInputSchema).optional()
}).strict();

export const TopicUncheckedCreateInputSchema: z.ZodType<Prisma.TopicUncheckedCreateInput> = z.object({
  id: z.string().cuid().optional(),
  name: z.string(),
  createdAt: z.coerce.date().optional(),
  updatedAt: z.coerce.date().optional(),
  nodes: z.lazy(() => KnowledgeNodeUncheckedCreateNestedManyWithoutTopicsInputSchema).optional()
}).strict();

export const TopicUpdateInputSchema: z.ZodType<Prisma.TopicUpdateInput> = z.object({
  id: z.union([ z.string().cuid(),z.lazy(() => StringFieldUpdateOperationsInputSchema) ]).optional(),
  name: z.union([ z.string(),z.lazy(() => StringFieldUpdateOperationsInputSchema) ]).optional(),
  createdAt: z.union([ z.coerce.date(),z.lazy(() => DateTimeFieldUpdateOperationsInputSchema) ]).optional(),
  updatedAt: z.union([ z.coerce.date(),z.lazy(() => DateTimeFieldUpdateOperationsInputSchema) ]).optional(),
  nodes: z.lazy(() => KnowledgeNodeUpdateManyWithoutTopicsNestedInputSchema).optional()
}).strict();

export const TopicUncheckedUpdateInputSchema: z.ZodType<Prisma.TopicUncheckedUpdateInput> = z.object({
  id: z.union([ z.string().cuid(),z.lazy(() => StringFieldUpdateOperationsInputSchema) ]).optional(),
  name: z.union([ z.string(),z.lazy(() => StringFieldUpdateOperationsInputSchema) ]).optional(),
  createdAt: z.union([ z.coerce.date(),z.lazy(() => DateTimeFieldUpdateOperationsInputSchema) ]).optional(),
  updatedAt: z.union([ z.coerce.date(),z.lazy(() => DateTimeFieldUpdateOperationsInputSchema) ]).optional(),
  nodes: z.lazy(() => KnowledgeNodeUncheckedUpdateManyWithoutTopicsNestedInputSchema).optional()
}).strict();

export const TopicCreateManyInputSchema: z.ZodType<Prisma.TopicCreateManyInput> = z.object({
  id: z.string().cuid().optional(),
  name: z.string(),
  createdAt: z.coerce.date().optional(),
  updatedAt: z.coerce.date().optional()
}).strict();

export const TopicUpdateManyMutationInputSchema: z.ZodType<Prisma.TopicUpdateManyMutationInput> = z.object({
  id: z.union([ z.string().cuid(),z.lazy(() => StringFieldUpdateOperationsInputSchema) ]).optional(),
  name: z.union([ z.string(),z.lazy(() => StringFieldUpdateOperationsInputSchema) ]).optional(),
  createdAt: z.union([ z.coerce.date(),z.lazy(() => DateTimeFieldUpdateOperationsInputSchema) ]).optional(),
  updatedAt: z.union([ z.coerce.date(),z.lazy(() => DateTimeFieldUpdateOperationsInputSchema) ]).optional(),
}).strict();

export const TopicUncheckedUpdateManyInputSchema: z.ZodType<Prisma.TopicUncheckedUpdateManyInput> = z.object({
  id: z.union([ z.string().cuid(),z.lazy(() => StringFieldUpdateOperationsInputSchema) ]).optional(),
  name: z.union([ z.string(),z.lazy(() => StringFieldUpdateOperationsInputSchema) ]).optional(),
  createdAt: z.union([ z.coerce.date(),z.lazy(() => DateTimeFieldUpdateOperationsInputSchema) ]).optional(),
  updatedAt: z.union([ z.coerce.date(),z.lazy(() => DateTimeFieldUpdateOperationsInputSchema) ]).optional(),
}).strict();

export const IntFilterSchema: z.ZodType<Prisma.IntFilter> = z.object({
  equals: z.number().optional(),
  in: z.number().array().optional(),
  notIn: z.number().array().optional(),
  lt: z.number().optional(),
  lte: z.number().optional(),
  gt: z.number().optional(),
  gte: z.number().optional(),
  not: z.union([ z.number(),z.lazy(() => NestedIntFilterSchema) ]).optional(),
}).strict();

export const StringFilterSchema: z.ZodType<Prisma.StringFilter> = z.object({
  equals: z.string().optional(),
  in: z.string().array().optional(),
  notIn: z.string().array().optional(),
  lt: z.string().optional(),
  lte: z.string().optional(),
  gt: z.string().optional(),
  gte: z.string().optional(),
  contains: z.string().optional(),
  startsWith: z.string().optional(),
  endsWith: z.string().optional(),
  mode: z.lazy(() => QueryModeSchema).optional(),
  not: z.union([ z.string(),z.lazy(() => NestedStringFilterSchema) ]).optional(),
}).strict();

export const FooCountOrderByAggregateInputSchema: z.ZodType<Prisma.FooCountOrderByAggregateInput> = z.object({
  id: z.lazy(() => SortOrderSchema).optional(),
  name: z.lazy(() => SortOrderSchema).optional(),
  email: z.lazy(() => SortOrderSchema).optional()
}).strict();

export const FooAvgOrderByAggregateInputSchema: z.ZodType<Prisma.FooAvgOrderByAggregateInput> = z.object({
  id: z.lazy(() => SortOrderSchema).optional()
}).strict();

export const FooMaxOrderByAggregateInputSchema: z.ZodType<Prisma.FooMaxOrderByAggregateInput> = z.object({
  id: z.lazy(() => SortOrderSchema).optional(),
  name: z.lazy(() => SortOrderSchema).optional(),
  email: z.lazy(() => SortOrderSchema).optional()
}).strict();

export const FooMinOrderByAggregateInputSchema: z.ZodType<Prisma.FooMinOrderByAggregateInput> = z.object({
  id: z.lazy(() => SortOrderSchema).optional(),
  name: z.lazy(() => SortOrderSchema).optional(),
  email: z.lazy(() => SortOrderSchema).optional()
}).strict();

export const FooSumOrderByAggregateInputSchema: z.ZodType<Prisma.FooSumOrderByAggregateInput> = z.object({
  id: z.lazy(() => SortOrderSchema).optional()
}).strict();

export const IntWithAggregatesFilterSchema: z.ZodType<Prisma.IntWithAggregatesFilter> = z.object({
  equals: z.number().optional(),
  in: z.number().array().optional(),
  notIn: z.number().array().optional(),
  lt: z.number().optional(),
  lte: z.number().optional(),
  gt: z.number().optional(),
  gte: z.number().optional(),
  not: z.union([ z.number(),z.lazy(() => NestedIntWithAggregatesFilterSchema) ]).optional(),
  _count: z.lazy(() => NestedIntFilterSchema).optional(),
  _avg: z.lazy(() => NestedFloatFilterSchema).optional(),
  _sum: z.lazy(() => NestedIntFilterSchema).optional(),
  _min: z.lazy(() => NestedIntFilterSchema).optional(),
  _max: z.lazy(() => NestedIntFilterSchema).optional()
}).strict();

export const StringWithAggregatesFilterSchema: z.ZodType<Prisma.StringWithAggregatesFilter> = z.object({
  equals: z.string().optional(),
  in: z.string().array().optional(),
  notIn: z.string().array().optional(),
  lt: z.string().optional(),
  lte: z.string().optional(),
  gt: z.string().optional(),
  gte: z.string().optional(),
  contains: z.string().optional(),
  startsWith: z.string().optional(),
  endsWith: z.string().optional(),
  mode: z.lazy(() => QueryModeSchema).optional(),
  not: z.union([ z.string(),z.lazy(() => NestedStringWithAggregatesFilterSchema) ]).optional(),
  _count: z.lazy(() => NestedIntFilterSchema).optional(),
  _min: z.lazy(() => NestedStringFilterSchema).optional(),
  _max: z.lazy(() => NestedStringFilterSchema).optional()
}).strict();

export const StringNullableFilterSchema: z.ZodType<Prisma.StringNullableFilter> = z.object({
  equals: z.string().optional().nullable(),
  in: z.string().array().optional().nullable(),
  notIn: z.string().array().optional().nullable(),
  lt: z.string().optional(),
  lte: z.string().optional(),
  gt: z.string().optional(),
  gte: z.string().optional(),
  contains: z.string().optional(),
  startsWith: z.string().optional(),
  endsWith: z.string().optional(),
  mode: z.lazy(() => QueryModeSchema).optional(),
  not: z.union([ z.string(),z.lazy(() => NestedStringNullableFilterSchema) ]).optional().nullable(),
}).strict();

export const JsonNullableFilterSchema: z.ZodType<Prisma.JsonNullableFilter> = z.object({
  equals: InputJsonValueSchema.optional(),
  path: z.string().array().optional(),
  mode: z.lazy(() => QueryModeSchema).optional(),
  string_contains: z.string().optional(),
  string_starts_with: z.string().optional(),
  string_ends_with: z.string().optional(),
  array_starts_with: InputJsonValueSchema.optional().nullable(),
  array_ends_with: InputJsonValueSchema.optional().nullable(),
  array_contains: InputJsonValueSchema.optional().nullable(),
  lt: InputJsonValueSchema.optional(),
  lte: InputJsonValueSchema.optional(),
  gt: InputJsonValueSchema.optional(),
  gte: InputJsonValueSchema.optional(),
  not: InputJsonValueSchema.optional()
}).strict();

export const DateTimeFilterSchema: z.ZodType<Prisma.DateTimeFilter> = z.object({
  equals: z.coerce.date().optional(),
  in: z.coerce.date().array().optional(),
  notIn: z.coerce.date().array().optional(),
  lt: z.coerce.date().optional(),
  lte: z.coerce.date().optional(),
  gt: z.coerce.date().optional(),
  gte: z.coerce.date().optional(),
  not: z.union([ z.coerce.date(),z.lazy(() => NestedDateTimeFilterSchema) ]).optional(),
}).strict();

export const SortOrderInputSchema: z.ZodType<Prisma.SortOrderInput> = z.object({
  sort: z.lazy(() => SortOrderSchema),
  nulls: z.lazy(() => NullsOrderSchema).optional()
}).strict();

export const TempEmbeddingsTableCountOrderByAggregateInputSchema: z.ZodType<Prisma.TempEmbeddingsTableCountOrderByAggregateInput> = z.object({
  id: z.lazy(() => SortOrderSchema).optional(),
  parentId: z.lazy(() => SortOrderSchema).optional(),
  type: z.lazy(() => SortOrderSchema).optional(),
  text: z.lazy(() => SortOrderSchema).optional(),
  metadata: z.lazy(() => SortOrderSchema).optional(),
  createdAt: z.lazy(() => SortOrderSchema).optional(),
  updatedAt: z.lazy(() => SortOrderSchema).optional()
}).strict();

export const TempEmbeddingsTableMaxOrderByAggregateInputSchema: z.ZodType<Prisma.TempEmbeddingsTableMaxOrderByAggregateInput> = z.object({
  id: z.lazy(() => SortOrderSchema).optional(),
  parentId: z.lazy(() => SortOrderSchema).optional(),
  type: z.lazy(() => SortOrderSchema).optional(),
  text: z.lazy(() => SortOrderSchema).optional(),
  createdAt: z.lazy(() => SortOrderSchema).optional(),
  updatedAt: z.lazy(() => SortOrderSchema).optional()
}).strict();

export const TempEmbeddingsTableMinOrderByAggregateInputSchema: z.ZodType<Prisma.TempEmbeddingsTableMinOrderByAggregateInput> = z.object({
  id: z.lazy(() => SortOrderSchema).optional(),
  parentId: z.lazy(() => SortOrderSchema).optional(),
  type: z.lazy(() => SortOrderSchema).optional(),
  text: z.lazy(() => SortOrderSchema).optional(),
  createdAt: z.lazy(() => SortOrderSchema).optional(),
  updatedAt: z.lazy(() => SortOrderSchema).optional()
}).strict();

export const StringNullableWithAggregatesFilterSchema: z.ZodType<Prisma.StringNullableWithAggregatesFilter> = z.object({
  equals: z.string().optional().nullable(),
  in: z.string().array().optional().nullable(),
  notIn: z.string().array().optional().nullable(),
  lt: z.string().optional(),
  lte: z.string().optional(),
  gt: z.string().optional(),
  gte: z.string().optional(),
  contains: z.string().optional(),
  startsWith: z.string().optional(),
  endsWith: z.string().optional(),
  mode: z.lazy(() => QueryModeSchema).optional(),
  not: z.union([ z.string(),z.lazy(() => NestedStringNullableWithAggregatesFilterSchema) ]).optional().nullable(),
  _count: z.lazy(() => NestedIntNullableFilterSchema).optional(),
  _min: z.lazy(() => NestedStringNullableFilterSchema).optional(),
  _max: z.lazy(() => NestedStringNullableFilterSchema).optional()
}).strict();

export const JsonNullableWithAggregatesFilterSchema: z.ZodType<Prisma.JsonNullableWithAggregatesFilter> = z.object({
  equals: InputJsonValueSchema.optional(),
  path: z.string().array().optional(),
  mode: z.lazy(() => QueryModeSchema).optional(),
  string_contains: z.string().optional(),
  string_starts_with: z.string().optional(),
  string_ends_with: z.string().optional(),
  array_starts_with: InputJsonValueSchema.optional().nullable(),
  array_ends_with: InputJsonValueSchema.optional().nullable(),
  array_contains: InputJsonValueSchema.optional().nullable(),
  lt: InputJsonValueSchema.optional(),
  lte: InputJsonValueSchema.optional(),
  gt: InputJsonValueSchema.optional(),
  gte: InputJsonValueSchema.optional(),
  not: InputJsonValueSchema.optional(),
  _count: z.lazy(() => NestedIntNullableFilterSchema).optional(),
  _min: z.lazy(() => NestedJsonNullableFilterSchema).optional(),
  _max: z.lazy(() => NestedJsonNullableFilterSchema).optional()
}).strict();

export const DateTimeWithAggregatesFilterSchema: z.ZodType<Prisma.DateTimeWithAggregatesFilter> = z.object({
  equals: z.coerce.date().optional(),
  in: z.coerce.date().array().optional(),
  notIn: z.coerce.date().array().optional(),
  lt: z.coerce.date().optional(),
  lte: z.coerce.date().optional(),
  gt: z.coerce.date().optional(),
  gte: z.coerce.date().optional(),
  not: z.union([ z.coerce.date(),z.lazy(() => NestedDateTimeWithAggregatesFilterSchema) ]).optional(),
  _count: z.lazy(() => NestedIntFilterSchema).optional(),
  _min: z.lazy(() => NestedDateTimeFilterSchema).optional(),
  _max: z.lazy(() => NestedDateTimeFilterSchema).optional()
}).strict();

export const KnowledgeNodeListRelationFilterSchema: z.ZodType<Prisma.KnowledgeNodeListRelationFilter> = z.object({
  every: z.lazy(() => KnowledgeNodeWhereInputSchema).optional(),
  some: z.lazy(() => KnowledgeNodeWhereInputSchema).optional(),
  none: z.lazy(() => KnowledgeNodeWhereInputSchema).optional()
}).strict();

export const ValidationListRelationFilterSchema: z.ZodType<Prisma.ValidationListRelationFilter> = z.object({
  every: z.lazy(() => ValidationWhereInputSchema).optional(),
  some: z.lazy(() => ValidationWhereInputSchema).optional(),
  none: z.lazy(() => ValidationWhereInputSchema).optional()
}).strict();

export const KnowledgeNodeOrderByRelationAggregateInputSchema: z.ZodType<Prisma.KnowledgeNodeOrderByRelationAggregateInput> = z.object({
  _count: z.lazy(() => SortOrderSchema).optional()
}).strict();

export const ValidationOrderByRelationAggregateInputSchema: z.ZodType<Prisma.ValidationOrderByRelationAggregateInput> = z.object({
  _count: z.lazy(() => SortOrderSchema).optional()
}).strict();

export const UserCountOrderByAggregateInputSchema: z.ZodType<Prisma.UserCountOrderByAggregateInput> = z.object({
  userId: z.lazy(() => SortOrderSchema).optional()
}).strict();

export const UserMaxOrderByAggregateInputSchema: z.ZodType<Prisma.UserMaxOrderByAggregateInput> = z.object({
  userId: z.lazy(() => SortOrderSchema).optional()
}).strict();

export const UserMinOrderByAggregateInputSchema: z.ZodType<Prisma.UserMinOrderByAggregateInput> = z.object({
  userId: z.lazy(() => SortOrderSchema).optional()
}).strict();

export const FloatNullableListFilterSchema: z.ZodType<Prisma.FloatNullableListFilter> = z.object({
  equals: z.number().array().optional().nullable(),
  has: z.number().optional().nullable(),
  hasEvery: z.number().array().optional(),
  hasSome: z.number().array().optional(),
  isEmpty: z.boolean().optional()
}).strict();

export const EnumNodeTypeFilterSchema: z.ZodType<Prisma.EnumNodeTypeFilter> = z.object({
  equals: z.lazy(() => NodeTypeSchema).optional(),
  in: z.lazy(() => NodeTypeSchema).array().optional(),
  notIn: z.lazy(() => NodeTypeSchema).array().optional(),
  not: z.union([ z.lazy(() => NodeTypeSchema),z.lazy(() => NestedEnumNodeTypeFilterSchema) ]).optional(),
}).strict();

export const EnumSourceTypeFilterSchema: z.ZodType<Prisma.EnumSourceTypeFilter> = z.object({
  equals: z.lazy(() => SourceTypeSchema).optional(),
  in: z.lazy(() => SourceTypeSchema).array().optional(),
  notIn: z.lazy(() => SourceTypeSchema).array().optional(),
  not: z.union([ z.lazy(() => SourceTypeSchema),z.lazy(() => NestedEnumSourceTypeFilterSchema) ]).optional(),
}).strict();

export const BoolFilterSchema: z.ZodType<Prisma.BoolFilter> = z.object({
  equals: z.boolean().optional(),
  not: z.union([ z.boolean(),z.lazy(() => NestedBoolFilterSchema) ]).optional(),
}).strict();

export const UserScalarRelationFilterSchema: z.ZodType<Prisma.UserScalarRelationFilter> = z.object({
  is: z.lazy(() => UserWhereInputSchema).optional(),
  isNot: z.lazy(() => UserWhereInputSchema).optional()
}).strict();

export const NodeRelationshipListRelationFilterSchema: z.ZodType<Prisma.NodeRelationshipListRelationFilter> = z.object({
  every: z.lazy(() => NodeRelationshipWhereInputSchema).optional(),
  some: z.lazy(() => NodeRelationshipWhereInputSchema).optional(),
  none: z.lazy(() => NodeRelationshipWhereInputSchema).optional()
}).strict();

export const TopicListRelationFilterSchema: z.ZodType<Prisma.TopicListRelationFilter> = z.object({
  every: z.lazy(() => TopicWhereInputSchema).optional(),
  some: z.lazy(() => TopicWhereInputSchema).optional(),
  none: z.lazy(() => TopicWhereInputSchema).optional()
}).strict();

export const NodeRelationshipOrderByRelationAggregateInputSchema: z.ZodType<Prisma.NodeRelationshipOrderByRelationAggregateInput> = z.object({
  _count: z.lazy(() => SortOrderSchema).optional()
}).strict();

export const TopicOrderByRelationAggregateInputSchema: z.ZodType<Prisma.TopicOrderByRelationAggregateInput> = z.object({
  _count: z.lazy(() => SortOrderSchema).optional()
}).strict();

export const KnowledgeNodeCountOrderByAggregateInputSchema: z.ZodType<Prisma.KnowledgeNodeCountOrderByAggregateInput> = z.object({
  id: z.lazy(() => SortOrderSchema).optional(),
  content: z.lazy(() => SortOrderSchema).optional(),
  embedding: z.lazy(() => SortOrderSchema).optional(),
  type: z.lazy(() => SortOrderSchema).optional(),
  source: z.lazy(() => SortOrderSchema).optional(),
  context: z.lazy(() => SortOrderSchema).optional(),
  validated: z.lazy(() => SortOrderSchema).optional(),
  createdAt: z.lazy(() => SortOrderSchema).optional(),
  updatedAt: z.lazy(() => SortOrderSchema).optional(),
  authorId: z.lazy(() => SortOrderSchema).optional()
}).strict();

export const KnowledgeNodeAvgOrderByAggregateInputSchema: z.ZodType<Prisma.KnowledgeNodeAvgOrderByAggregateInput> = z.object({
  embedding: z.lazy(() => SortOrderSchema).optional()
}).strict();

export const KnowledgeNodeMaxOrderByAggregateInputSchema: z.ZodType<Prisma.KnowledgeNodeMaxOrderByAggregateInput> = z.object({
  id: z.lazy(() => SortOrderSchema).optional(),
  content: z.lazy(() => SortOrderSchema).optional(),
  type: z.lazy(() => SortOrderSchema).optional(),
  source: z.lazy(() => SortOrderSchema).optional(),
  validated: z.lazy(() => SortOrderSchema).optional(),
  createdAt: z.lazy(() => SortOrderSchema).optional(),
  updatedAt: z.lazy(() => SortOrderSchema).optional(),
  authorId: z.lazy(() => SortOrderSchema).optional()
}).strict();

export const KnowledgeNodeMinOrderByAggregateInputSchema: z.ZodType<Prisma.KnowledgeNodeMinOrderByAggregateInput> = z.object({
  id: z.lazy(() => SortOrderSchema).optional(),
  content: z.lazy(() => SortOrderSchema).optional(),
  type: z.lazy(() => SortOrderSchema).optional(),
  source: z.lazy(() => SortOrderSchema).optional(),
  validated: z.lazy(() => SortOrderSchema).optional(),
  createdAt: z.lazy(() => SortOrderSchema).optional(),
  updatedAt: z.lazy(() => SortOrderSchema).optional(),
  authorId: z.lazy(() => SortOrderSchema).optional()
}).strict();

export const KnowledgeNodeSumOrderByAggregateInputSchema: z.ZodType<Prisma.KnowledgeNodeSumOrderByAggregateInput> = z.object({
  embedding: z.lazy(() => SortOrderSchema).optional()
}).strict();

export const EnumNodeTypeWithAggregatesFilterSchema: z.ZodType<Prisma.EnumNodeTypeWithAggregatesFilter> = z.object({
  equals: z.lazy(() => NodeTypeSchema).optional(),
  in: z.lazy(() => NodeTypeSchema).array().optional(),
  notIn: z.lazy(() => NodeTypeSchema).array().optional(),
  not: z.union([ z.lazy(() => NodeTypeSchema),z.lazy(() => NestedEnumNodeTypeWithAggregatesFilterSchema) ]).optional(),
  _count: z.lazy(() => NestedIntFilterSchema).optional(),
  _min: z.lazy(() => NestedEnumNodeTypeFilterSchema).optional(),
  _max: z.lazy(() => NestedEnumNodeTypeFilterSchema).optional()
}).strict();

export const EnumSourceTypeWithAggregatesFilterSchema: z.ZodType<Prisma.EnumSourceTypeWithAggregatesFilter> = z.object({
  equals: z.lazy(() => SourceTypeSchema).optional(),
  in: z.lazy(() => SourceTypeSchema).array().optional(),
  notIn: z.lazy(() => SourceTypeSchema).array().optional(),
  not: z.union([ z.lazy(() => SourceTypeSchema),z.lazy(() => NestedEnumSourceTypeWithAggregatesFilterSchema) ]).optional(),
  _count: z.lazy(() => NestedIntFilterSchema).optional(),
  _min: z.lazy(() => NestedEnumSourceTypeFilterSchema).optional(),
  _max: z.lazy(() => NestedEnumSourceTypeFilterSchema).optional()
}).strict();

export const BoolWithAggregatesFilterSchema: z.ZodType<Prisma.BoolWithAggregatesFilter> = z.object({
  equals: z.boolean().optional(),
  not: z.union([ z.boolean(),z.lazy(() => NestedBoolWithAggregatesFilterSchema) ]).optional(),
  _count: z.lazy(() => NestedIntFilterSchema).optional(),
  _min: z.lazy(() => NestedBoolFilterSchema).optional(),
  _max: z.lazy(() => NestedBoolFilterSchema).optional()
}).strict();

export const EnumRelationshipTypeFilterSchema: z.ZodType<Prisma.EnumRelationshipTypeFilter> = z.object({
  equals: z.lazy(() => RelationshipTypeSchema).optional(),
  in: z.lazy(() => RelationshipTypeSchema).array().optional(),
  notIn: z.lazy(() => RelationshipTypeSchema).array().optional(),
  not: z.union([ z.lazy(() => RelationshipTypeSchema),z.lazy(() => NestedEnumRelationshipTypeFilterSchema) ]).optional(),
}).strict();

export const KnowledgeNodeScalarRelationFilterSchema: z.ZodType<Prisma.KnowledgeNodeScalarRelationFilter> = z.object({
  is: z.lazy(() => KnowledgeNodeWhereInputSchema).optional(),
  isNot: z.lazy(() => KnowledgeNodeWhereInputSchema).optional()
}).strict();

export const NodeRelationshipCountOrderByAggregateInputSchema: z.ZodType<Prisma.NodeRelationshipCountOrderByAggregateInput> = z.object({
  id: z.lazy(() => SortOrderSchema).optional(),
  fromId: z.lazy(() => SortOrderSchema).optional(),
  toId: z.lazy(() => SortOrderSchema).optional(),
  type: z.lazy(() => SortOrderSchema).optional(),
  createdAt: z.lazy(() => SortOrderSchema).optional(),
  updatedAt: z.lazy(() => SortOrderSchema).optional()
}).strict();

export const NodeRelationshipMaxOrderByAggregateInputSchema: z.ZodType<Prisma.NodeRelationshipMaxOrderByAggregateInput> = z.object({
  id: z.lazy(() => SortOrderSchema).optional(),
  fromId: z.lazy(() => SortOrderSchema).optional(),
  toId: z.lazy(() => SortOrderSchema).optional(),
  type: z.lazy(() => SortOrderSchema).optional(),
  createdAt: z.lazy(() => SortOrderSchema).optional(),
  updatedAt: z.lazy(() => SortOrderSchema).optional()
}).strict();

export const NodeRelationshipMinOrderByAggregateInputSchema: z.ZodType<Prisma.NodeRelationshipMinOrderByAggregateInput> = z.object({
  id: z.lazy(() => SortOrderSchema).optional(),
  fromId: z.lazy(() => SortOrderSchema).optional(),
  toId: z.lazy(() => SortOrderSchema).optional(),
  type: z.lazy(() => SortOrderSchema).optional(),
  createdAt: z.lazy(() => SortOrderSchema).optional(),
  updatedAt: z.lazy(() => SortOrderSchema).optional()
}).strict();

export const EnumRelationshipTypeWithAggregatesFilterSchema: z.ZodType<Prisma.EnumRelationshipTypeWithAggregatesFilter> = z.object({
  equals: z.lazy(() => RelationshipTypeSchema).optional(),
  in: z.lazy(() => RelationshipTypeSchema).array().optional(),
  notIn: z.lazy(() => RelationshipTypeSchema).array().optional(),
  not: z.union([ z.lazy(() => RelationshipTypeSchema),z.lazy(() => NestedEnumRelationshipTypeWithAggregatesFilterSchema) ]).optional(),
  _count: z.lazy(() => NestedIntFilterSchema).optional(),
  _min: z.lazy(() => NestedEnumRelationshipTypeFilterSchema).optional(),
  _max: z.lazy(() => NestedEnumRelationshipTypeFilterSchema).optional()
}).strict();

export const ValidationCountOrderByAggregateInputSchema: z.ZodType<Prisma.ValidationCountOrderByAggregateInput> = z.object({
  id: z.lazy(() => SortOrderSchema).optional(),
  nodeId: z.lazy(() => SortOrderSchema).optional(),
  expertId: z.lazy(() => SortOrderSchema).optional(),
  notes: z.lazy(() => SortOrderSchema).optional(),
  createdAt: z.lazy(() => SortOrderSchema).optional(),
  updatedAt: z.lazy(() => SortOrderSchema).optional()
}).strict();

export const ValidationMaxOrderByAggregateInputSchema: z.ZodType<Prisma.ValidationMaxOrderByAggregateInput> = z.object({
  id: z.lazy(() => SortOrderSchema).optional(),
  nodeId: z.lazy(() => SortOrderSchema).optional(),
  expertId: z.lazy(() => SortOrderSchema).optional(),
  notes: z.lazy(() => SortOrderSchema).optional(),
  createdAt: z.lazy(() => SortOrderSchema).optional(),
  updatedAt: z.lazy(() => SortOrderSchema).optional()
}).strict();

export const ValidationMinOrderByAggregateInputSchema: z.ZodType<Prisma.ValidationMinOrderByAggregateInput> = z.object({
  id: z.lazy(() => SortOrderSchema).optional(),
  nodeId: z.lazy(() => SortOrderSchema).optional(),
  expertId: z.lazy(() => SortOrderSchema).optional(),
  notes: z.lazy(() => SortOrderSchema).optional(),
  createdAt: z.lazy(() => SortOrderSchema).optional(),
  updatedAt: z.lazy(() => SortOrderSchema).optional()
}).strict();

export const TopicCountOrderByAggregateInputSchema: z.ZodType<Prisma.TopicCountOrderByAggregateInput> = z.object({
  id: z.lazy(() => SortOrderSchema).optional(),
  name: z.lazy(() => SortOrderSchema).optional(),
  createdAt: z.lazy(() => SortOrderSchema).optional(),
  updatedAt: z.lazy(() => SortOrderSchema).optional()
}).strict();

export const TopicMaxOrderByAggregateInputSchema: z.ZodType<Prisma.TopicMaxOrderByAggregateInput> = z.object({
  id: z.lazy(() => SortOrderSchema).optional(),
  name: z.lazy(() => SortOrderSchema).optional(),
  createdAt: z.lazy(() => SortOrderSchema).optional(),
  updatedAt: z.lazy(() => SortOrderSchema).optional()
}).strict();

export const TopicMinOrderByAggregateInputSchema: z.ZodType<Prisma.TopicMinOrderByAggregateInput> = z.object({
  id: z.lazy(() => SortOrderSchema).optional(),
  name: z.lazy(() => SortOrderSchema).optional(),
  createdAt: z.lazy(() => SortOrderSchema).optional(),
  updatedAt: z.lazy(() => SortOrderSchema).optional()
}).strict();

export const StringFieldUpdateOperationsInputSchema: z.ZodType<Prisma.StringFieldUpdateOperationsInput> = z.object({
  set: z.string().optional()
}).strict();

export const IntFieldUpdateOperationsInputSchema: z.ZodType<Prisma.IntFieldUpdateOperationsInput> = z.object({
  set: z.number().optional(),
  increment: z.number().optional(),
  decrement: z.number().optional(),
  multiply: z.number().optional(),
  divide: z.number().optional()
}).strict();

export const NullableStringFieldUpdateOperationsInputSchema: z.ZodType<Prisma.NullableStringFieldUpdateOperationsInput> = z.object({
  set: z.string().optional().nullable()
}).strict();

export const DateTimeFieldUpdateOperationsInputSchema: z.ZodType<Prisma.DateTimeFieldUpdateOperationsInput> = z.object({
  set: z.coerce.date().optional()
}).strict();

export const KnowledgeNodeCreateNestedManyWithoutAuthorInputSchema: z.ZodType<Prisma.KnowledgeNodeCreateNestedManyWithoutAuthorInput> = z.object({
  create: z.union([ z.lazy(() => KnowledgeNodeCreateWithoutAuthorInputSchema),z.lazy(() => KnowledgeNodeCreateWithoutAuthorInputSchema).array(),z.lazy(() => KnowledgeNodeUncheckedCreateWithoutAuthorInputSchema),z.lazy(() => KnowledgeNodeUncheckedCreateWithoutAuthorInputSchema).array() ]).optional(),
  connectOrCreate: z.union([ z.lazy(() => KnowledgeNodeCreateOrConnectWithoutAuthorInputSchema),z.lazy(() => KnowledgeNodeCreateOrConnectWithoutAuthorInputSchema).array() ]).optional(),
  createMany: z.lazy(() => KnowledgeNodeCreateManyAuthorInputEnvelopeSchema).optional(),
  connect: z.union([ z.lazy(() => KnowledgeNodeWhereUniqueInputSchema),z.lazy(() => KnowledgeNodeWhereUniqueInputSchema).array() ]).optional(),
}).strict();

export const ValidationCreateNestedManyWithoutExpertInputSchema: z.ZodType<Prisma.ValidationCreateNestedManyWithoutExpertInput> = z.object({
  create: z.union([ z.lazy(() => ValidationCreateWithoutExpertInputSchema),z.lazy(() => ValidationCreateWithoutExpertInputSchema).array(),z.lazy(() => ValidationUncheckedCreateWithoutExpertInputSchema),z.lazy(() => ValidationUncheckedCreateWithoutExpertInputSchema).array() ]).optional(),
  connectOrCreate: z.union([ z.lazy(() => ValidationCreateOrConnectWithoutExpertInputSchema),z.lazy(() => ValidationCreateOrConnectWithoutExpertInputSchema).array() ]).optional(),
  createMany: z.lazy(() => ValidationCreateManyExpertInputEnvelopeSchema).optional(),
  connect: z.union([ z.lazy(() => ValidationWhereUniqueInputSchema),z.lazy(() => ValidationWhereUniqueInputSchema).array() ]).optional(),
}).strict();

export const KnowledgeNodeUncheckedCreateNestedManyWithoutAuthorInputSchema: z.ZodType<Prisma.KnowledgeNodeUncheckedCreateNestedManyWithoutAuthorInput> = z.object({
  create: z.union([ z.lazy(() => KnowledgeNodeCreateWithoutAuthorInputSchema),z.lazy(() => KnowledgeNodeCreateWithoutAuthorInputSchema).array(),z.lazy(() => KnowledgeNodeUncheckedCreateWithoutAuthorInputSchema),z.lazy(() => KnowledgeNodeUncheckedCreateWithoutAuthorInputSchema).array() ]).optional(),
  connectOrCreate: z.union([ z.lazy(() => KnowledgeNodeCreateOrConnectWithoutAuthorInputSchema),z.lazy(() => KnowledgeNodeCreateOrConnectWithoutAuthorInputSchema).array() ]).optional(),
  createMany: z.lazy(() => KnowledgeNodeCreateManyAuthorInputEnvelopeSchema).optional(),
  connect: z.union([ z.lazy(() => KnowledgeNodeWhereUniqueInputSchema),z.lazy(() => KnowledgeNodeWhereUniqueInputSchema).array() ]).optional(),
}).strict();

export const ValidationUncheckedCreateNestedManyWithoutExpertInputSchema: z.ZodType<Prisma.ValidationUncheckedCreateNestedManyWithoutExpertInput> = z.object({
  create: z.union([ z.lazy(() => ValidationCreateWithoutExpertInputSchema),z.lazy(() => ValidationCreateWithoutExpertInputSchema).array(),z.lazy(() => ValidationUncheckedCreateWithoutExpertInputSchema),z.lazy(() => ValidationUncheckedCreateWithoutExpertInputSchema).array() ]).optional(),
  connectOrCreate: z.union([ z.lazy(() => ValidationCreateOrConnectWithoutExpertInputSchema),z.lazy(() => ValidationCreateOrConnectWithoutExpertInputSchema).array() ]).optional(),
  createMany: z.lazy(() => ValidationCreateManyExpertInputEnvelopeSchema).optional(),
  connect: z.union([ z.lazy(() => ValidationWhereUniqueInputSchema),z.lazy(() => ValidationWhereUniqueInputSchema).array() ]).optional(),
}).strict();

export const KnowledgeNodeUpdateManyWithoutAuthorNestedInputSchema: z.ZodType<Prisma.KnowledgeNodeUpdateManyWithoutAuthorNestedInput> = z.object({
  create: z.union([ z.lazy(() => KnowledgeNodeCreateWithoutAuthorInputSchema),z.lazy(() => KnowledgeNodeCreateWithoutAuthorInputSchema).array(),z.lazy(() => KnowledgeNodeUncheckedCreateWithoutAuthorInputSchema),z.lazy(() => KnowledgeNodeUncheckedCreateWithoutAuthorInputSchema).array() ]).optional(),
  connectOrCreate: z.union([ z.lazy(() => KnowledgeNodeCreateOrConnectWithoutAuthorInputSchema),z.lazy(() => KnowledgeNodeCreateOrConnectWithoutAuthorInputSchema).array() ]).optional(),
  upsert: z.union([ z.lazy(() => KnowledgeNodeUpsertWithWhereUniqueWithoutAuthorInputSchema),z.lazy(() => KnowledgeNodeUpsertWithWhereUniqueWithoutAuthorInputSchema).array() ]).optional(),
  createMany: z.lazy(() => KnowledgeNodeCreateManyAuthorInputEnvelopeSchema).optional(),
  set: z.union([ z.lazy(() => KnowledgeNodeWhereUniqueInputSchema),z.lazy(() => KnowledgeNodeWhereUniqueInputSchema).array() ]).optional(),
  disconnect: z.union([ z.lazy(() => KnowledgeNodeWhereUniqueInputSchema),z.lazy(() => KnowledgeNodeWhereUniqueInputSchema).array() ]).optional(),
  delete: z.union([ z.lazy(() => KnowledgeNodeWhereUniqueInputSchema),z.lazy(() => KnowledgeNodeWhereUniqueInputSchema).array() ]).optional(),
  connect: z.union([ z.lazy(() => KnowledgeNodeWhereUniqueInputSchema),z.lazy(() => KnowledgeNodeWhereUniqueInputSchema).array() ]).optional(),
  update: z.union([ z.lazy(() => KnowledgeNodeUpdateWithWhereUniqueWithoutAuthorInputSchema),z.lazy(() => KnowledgeNodeUpdateWithWhereUniqueWithoutAuthorInputSchema).array() ]).optional(),
  updateMany: z.union([ z.lazy(() => KnowledgeNodeUpdateManyWithWhereWithoutAuthorInputSchema),z.lazy(() => KnowledgeNodeUpdateManyWithWhereWithoutAuthorInputSchema).array() ]).optional(),
  deleteMany: z.union([ z.lazy(() => KnowledgeNodeScalarWhereInputSchema),z.lazy(() => KnowledgeNodeScalarWhereInputSchema).array() ]).optional(),
}).strict();

export const ValidationUpdateManyWithoutExpertNestedInputSchema: z.ZodType<Prisma.ValidationUpdateManyWithoutExpertNestedInput> = z.object({
  create: z.union([ z.lazy(() => ValidationCreateWithoutExpertInputSchema),z.lazy(() => ValidationCreateWithoutExpertInputSchema).array(),z.lazy(() => ValidationUncheckedCreateWithoutExpertInputSchema),z.lazy(() => ValidationUncheckedCreateWithoutExpertInputSchema).array() ]).optional(),
  connectOrCreate: z.union([ z.lazy(() => ValidationCreateOrConnectWithoutExpertInputSchema),z.lazy(() => ValidationCreateOrConnectWithoutExpertInputSchema).array() ]).optional(),
  upsert: z.union([ z.lazy(() => ValidationUpsertWithWhereUniqueWithoutExpertInputSchema),z.lazy(() => ValidationUpsertWithWhereUniqueWithoutExpertInputSchema).array() ]).optional(),
  createMany: z.lazy(() => ValidationCreateManyExpertInputEnvelopeSchema).optional(),
  set: z.union([ z.lazy(() => ValidationWhereUniqueInputSchema),z.lazy(() => ValidationWhereUniqueInputSchema).array() ]).optional(),
  disconnect: z.union([ z.lazy(() => ValidationWhereUniqueInputSchema),z.lazy(() => ValidationWhereUniqueInputSchema).array() ]).optional(),
  delete: z.union([ z.lazy(() => ValidationWhereUniqueInputSchema),z.lazy(() => ValidationWhereUniqueInputSchema).array() ]).optional(),
  connect: z.union([ z.lazy(() => ValidationWhereUniqueInputSchema),z.lazy(() => ValidationWhereUniqueInputSchema).array() ]).optional(),
  update: z.union([ z.lazy(() => ValidationUpdateWithWhereUniqueWithoutExpertInputSchema),z.lazy(() => ValidationUpdateWithWhereUniqueWithoutExpertInputSchema).array() ]).optional(),
  updateMany: z.union([ z.lazy(() => ValidationUpdateManyWithWhereWithoutExpertInputSchema),z.lazy(() => ValidationUpdateManyWithWhereWithoutExpertInputSchema).array() ]).optional(),
  deleteMany: z.union([ z.lazy(() => ValidationScalarWhereInputSchema),z.lazy(() => ValidationScalarWhereInputSchema).array() ]).optional(),
}).strict();

export const KnowledgeNodeUncheckedUpdateManyWithoutAuthorNestedInputSchema: z.ZodType<Prisma.KnowledgeNodeUncheckedUpdateManyWithoutAuthorNestedInput> = z.object({
  create: z.union([ z.lazy(() => KnowledgeNodeCreateWithoutAuthorInputSchema),z.lazy(() => KnowledgeNodeCreateWithoutAuthorInputSchema).array(),z.lazy(() => KnowledgeNodeUncheckedCreateWithoutAuthorInputSchema),z.lazy(() => KnowledgeNodeUncheckedCreateWithoutAuthorInputSchema).array() ]).optional(),
  connectOrCreate: z.union([ z.lazy(() => KnowledgeNodeCreateOrConnectWithoutAuthorInputSchema),z.lazy(() => KnowledgeNodeCreateOrConnectWithoutAuthorInputSchema).array() ]).optional(),
  upsert: z.union([ z.lazy(() => KnowledgeNodeUpsertWithWhereUniqueWithoutAuthorInputSchema),z.lazy(() => KnowledgeNodeUpsertWithWhereUniqueWithoutAuthorInputSchema).array() ]).optional(),
  createMany: z.lazy(() => KnowledgeNodeCreateManyAuthorInputEnvelopeSchema).optional(),
  set: z.union([ z.lazy(() => KnowledgeNodeWhereUniqueInputSchema),z.lazy(() => KnowledgeNodeWhereUniqueInputSchema).array() ]).optional(),
  disconnect: z.union([ z.lazy(() => KnowledgeNodeWhereUniqueInputSchema),z.lazy(() => KnowledgeNodeWhereUniqueInputSchema).array() ]).optional(),
  delete: z.union([ z.lazy(() => KnowledgeNodeWhereUniqueInputSchema),z.lazy(() => KnowledgeNodeWhereUniqueInputSchema).array() ]).optional(),
  connect: z.union([ z.lazy(() => KnowledgeNodeWhereUniqueInputSchema),z.lazy(() => KnowledgeNodeWhereUniqueInputSchema).array() ]).optional(),
  update: z.union([ z.lazy(() => KnowledgeNodeUpdateWithWhereUniqueWithoutAuthorInputSchema),z.lazy(() => KnowledgeNodeUpdateWithWhereUniqueWithoutAuthorInputSchema).array() ]).optional(),
  updateMany: z.union([ z.lazy(() => KnowledgeNodeUpdateManyWithWhereWithoutAuthorInputSchema),z.lazy(() => KnowledgeNodeUpdateManyWithWhereWithoutAuthorInputSchema).array() ]).optional(),
  deleteMany: z.union([ z.lazy(() => KnowledgeNodeScalarWhereInputSchema),z.lazy(() => KnowledgeNodeScalarWhereInputSchema).array() ]).optional(),
}).strict();

export const ValidationUncheckedUpdateManyWithoutExpertNestedInputSchema: z.ZodType<Prisma.ValidationUncheckedUpdateManyWithoutExpertNestedInput> = z.object({
  create: z.union([ z.lazy(() => ValidationCreateWithoutExpertInputSchema),z.lazy(() => ValidationCreateWithoutExpertInputSchema).array(),z.lazy(() => ValidationUncheckedCreateWithoutExpertInputSchema),z.lazy(() => ValidationUncheckedCreateWithoutExpertInputSchema).array() ]).optional(),
  connectOrCreate: z.union([ z.lazy(() => ValidationCreateOrConnectWithoutExpertInputSchema),z.lazy(() => ValidationCreateOrConnectWithoutExpertInputSchema).array() ]).optional(),
  upsert: z.union([ z.lazy(() => ValidationUpsertWithWhereUniqueWithoutExpertInputSchema),z.lazy(() => ValidationUpsertWithWhereUniqueWithoutExpertInputSchema).array() ]).optional(),
  createMany: z.lazy(() => ValidationCreateManyExpertInputEnvelopeSchema).optional(),
  set: z.union([ z.lazy(() => ValidationWhereUniqueInputSchema),z.lazy(() => ValidationWhereUniqueInputSchema).array() ]).optional(),
  disconnect: z.union([ z.lazy(() => ValidationWhereUniqueInputSchema),z.lazy(() => ValidationWhereUniqueInputSchema).array() ]).optional(),
  delete: z.union([ z.lazy(() => ValidationWhereUniqueInputSchema),z.lazy(() => ValidationWhereUniqueInputSchema).array() ]).optional(),
  connect: z.union([ z.lazy(() => ValidationWhereUniqueInputSchema),z.lazy(() => ValidationWhereUniqueInputSchema).array() ]).optional(),
  update: z.union([ z.lazy(() => ValidationUpdateWithWhereUniqueWithoutExpertInputSchema),z.lazy(() => ValidationUpdateWithWhereUniqueWithoutExpertInputSchema).array() ]).optional(),
  updateMany: z.union([ z.lazy(() => ValidationUpdateManyWithWhereWithoutExpertInputSchema),z.lazy(() => ValidationUpdateManyWithWhereWithoutExpertInputSchema).array() ]).optional(),
  deleteMany: z.union([ z.lazy(() => ValidationScalarWhereInputSchema),z.lazy(() => ValidationScalarWhereInputSchema).array() ]).optional(),
}).strict();

export const KnowledgeNodeCreateembeddingInputSchema: z.ZodType<Prisma.KnowledgeNodeCreateembeddingInput> = z.object({
  set: z.number().array()
}).strict();

export const UserCreateNestedOneWithoutNodesInputSchema: z.ZodType<Prisma.UserCreateNestedOneWithoutNodesInput> = z.object({
  create: z.union([ z.lazy(() => UserCreateWithoutNodesInputSchema),z.lazy(() => UserUncheckedCreateWithoutNodesInputSchema) ]).optional(),
  connectOrCreate: z.lazy(() => UserCreateOrConnectWithoutNodesInputSchema).optional(),
  connect: z.lazy(() => UserWhereUniqueInputSchema).optional()
}).strict();

export const NodeRelationshipCreateNestedManyWithoutFromInputSchema: z.ZodType<Prisma.NodeRelationshipCreateNestedManyWithoutFromInput> = z.object({
  create: z.union([ z.lazy(() => NodeRelationshipCreateWithoutFromInputSchema),z.lazy(() => NodeRelationshipCreateWithoutFromInputSchema).array(),z.lazy(() => NodeRelationshipUncheckedCreateWithoutFromInputSchema),z.lazy(() => NodeRelationshipUncheckedCreateWithoutFromInputSchema).array() ]).optional(),
  connectOrCreate: z.union([ z.lazy(() => NodeRelationshipCreateOrConnectWithoutFromInputSchema),z.lazy(() => NodeRelationshipCreateOrConnectWithoutFromInputSchema).array() ]).optional(),
  createMany: z.lazy(() => NodeRelationshipCreateManyFromInputEnvelopeSchema).optional(),
  connect: z.union([ z.lazy(() => NodeRelationshipWhereUniqueInputSchema),z.lazy(() => NodeRelationshipWhereUniqueInputSchema).array() ]).optional(),
}).strict();

export const NodeRelationshipCreateNestedManyWithoutToInputSchema: z.ZodType<Prisma.NodeRelationshipCreateNestedManyWithoutToInput> = z.object({
  create: z.union([ z.lazy(() => NodeRelationshipCreateWithoutToInputSchema),z.lazy(() => NodeRelationshipCreateWithoutToInputSchema).array(),z.lazy(() => NodeRelationshipUncheckedCreateWithoutToInputSchema),z.lazy(() => NodeRelationshipUncheckedCreateWithoutToInputSchema).array() ]).optional(),
  connectOrCreate: z.union([ z.lazy(() => NodeRelationshipCreateOrConnectWithoutToInputSchema),z.lazy(() => NodeRelationshipCreateOrConnectWithoutToInputSchema).array() ]).optional(),
  createMany: z.lazy(() => NodeRelationshipCreateManyToInputEnvelopeSchema).optional(),
  connect: z.union([ z.lazy(() => NodeRelationshipWhereUniqueInputSchema),z.lazy(() => NodeRelationshipWhereUniqueInputSchema).array() ]).optional(),
}).strict();

export const ValidationCreateNestedManyWithoutNodeInputSchema: z.ZodType<Prisma.ValidationCreateNestedManyWithoutNodeInput> = z.object({
  create: z.union([ z.lazy(() => ValidationCreateWithoutNodeInputSchema),z.lazy(() => ValidationCreateWithoutNodeInputSchema).array(),z.lazy(() => ValidationUncheckedCreateWithoutNodeInputSchema),z.lazy(() => ValidationUncheckedCreateWithoutNodeInputSchema).array() ]).optional(),
  connectOrCreate: z.union([ z.lazy(() => ValidationCreateOrConnectWithoutNodeInputSchema),z.lazy(() => ValidationCreateOrConnectWithoutNodeInputSchema).array() ]).optional(),
  createMany: z.lazy(() => ValidationCreateManyNodeInputEnvelopeSchema).optional(),
  connect: z.union([ z.lazy(() => ValidationWhereUniqueInputSchema),z.lazy(() => ValidationWhereUniqueInputSchema).array() ]).optional(),
}).strict();

export const TopicCreateNestedManyWithoutNodesInputSchema: z.ZodType<Prisma.TopicCreateNestedManyWithoutNodesInput> = z.object({
  create: z.union([ z.lazy(() => TopicCreateWithoutNodesInputSchema),z.lazy(() => TopicCreateWithoutNodesInputSchema).array(),z.lazy(() => TopicUncheckedCreateWithoutNodesInputSchema),z.lazy(() => TopicUncheckedCreateWithoutNodesInputSchema).array() ]).optional(),
  connectOrCreate: z.union([ z.lazy(() => TopicCreateOrConnectWithoutNodesInputSchema),z.lazy(() => TopicCreateOrConnectWithoutNodesInputSchema).array() ]).optional(),
  connect: z.union([ z.lazy(() => TopicWhereUniqueInputSchema),z.lazy(() => TopicWhereUniqueInputSchema).array() ]).optional(),
}).strict();

export const NodeRelationshipUncheckedCreateNestedManyWithoutFromInputSchema: z.ZodType<Prisma.NodeRelationshipUncheckedCreateNestedManyWithoutFromInput> = z.object({
  create: z.union([ z.lazy(() => NodeRelationshipCreateWithoutFromInputSchema),z.lazy(() => NodeRelationshipCreateWithoutFromInputSchema).array(),z.lazy(() => NodeRelationshipUncheckedCreateWithoutFromInputSchema),z.lazy(() => NodeRelationshipUncheckedCreateWithoutFromInputSchema).array() ]).optional(),
  connectOrCreate: z.union([ z.lazy(() => NodeRelationshipCreateOrConnectWithoutFromInputSchema),z.lazy(() => NodeRelationshipCreateOrConnectWithoutFromInputSchema).array() ]).optional(),
  createMany: z.lazy(() => NodeRelationshipCreateManyFromInputEnvelopeSchema).optional(),
  connect: z.union([ z.lazy(() => NodeRelationshipWhereUniqueInputSchema),z.lazy(() => NodeRelationshipWhereUniqueInputSchema).array() ]).optional(),
}).strict();

export const NodeRelationshipUncheckedCreateNestedManyWithoutToInputSchema: z.ZodType<Prisma.NodeRelationshipUncheckedCreateNestedManyWithoutToInput> = z.object({
  create: z.union([ z.lazy(() => NodeRelationshipCreateWithoutToInputSchema),z.lazy(() => NodeRelationshipCreateWithoutToInputSchema).array(),z.lazy(() => NodeRelationshipUncheckedCreateWithoutToInputSchema),z.lazy(() => NodeRelationshipUncheckedCreateWithoutToInputSchema).array() ]).optional(),
  connectOrCreate: z.union([ z.lazy(() => NodeRelationshipCreateOrConnectWithoutToInputSchema),z.lazy(() => NodeRelationshipCreateOrConnectWithoutToInputSchema).array() ]).optional(),
  createMany: z.lazy(() => NodeRelationshipCreateManyToInputEnvelopeSchema).optional(),
  connect: z.union([ z.lazy(() => NodeRelationshipWhereUniqueInputSchema),z.lazy(() => NodeRelationshipWhereUniqueInputSchema).array() ]).optional(),
}).strict();

export const ValidationUncheckedCreateNestedManyWithoutNodeInputSchema: z.ZodType<Prisma.ValidationUncheckedCreateNestedManyWithoutNodeInput> = z.object({
  create: z.union([ z.lazy(() => ValidationCreateWithoutNodeInputSchema),z.lazy(() => ValidationCreateWithoutNodeInputSchema).array(),z.lazy(() => ValidationUncheckedCreateWithoutNodeInputSchema),z.lazy(() => ValidationUncheckedCreateWithoutNodeInputSchema).array() ]).optional(),
  connectOrCreate: z.union([ z.lazy(() => ValidationCreateOrConnectWithoutNodeInputSchema),z.lazy(() => ValidationCreateOrConnectWithoutNodeInputSchema).array() ]).optional(),
  createMany: z.lazy(() => ValidationCreateManyNodeInputEnvelopeSchema).optional(),
  connect: z.union([ z.lazy(() => ValidationWhereUniqueInputSchema),z.lazy(() => ValidationWhereUniqueInputSchema).array() ]).optional(),
}).strict();

export const TopicUncheckedCreateNestedManyWithoutNodesInputSchema: z.ZodType<Prisma.TopicUncheckedCreateNestedManyWithoutNodesInput> = z.object({
  create: z.union([ z.lazy(() => TopicCreateWithoutNodesInputSchema),z.lazy(() => TopicCreateWithoutNodesInputSchema).array(),z.lazy(() => TopicUncheckedCreateWithoutNodesInputSchema),z.lazy(() => TopicUncheckedCreateWithoutNodesInputSchema).array() ]).optional(),
  connectOrCreate: z.union([ z.lazy(() => TopicCreateOrConnectWithoutNodesInputSchema),z.lazy(() => TopicCreateOrConnectWithoutNodesInputSchema).array() ]).optional(),
  connect: z.union([ z.lazy(() => TopicWhereUniqueInputSchema),z.lazy(() => TopicWhereUniqueInputSchema).array() ]).optional(),
}).strict();

export const KnowledgeNodeUpdateembeddingInputSchema: z.ZodType<Prisma.KnowledgeNodeUpdateembeddingInput> = z.object({
  set: z.number().array().optional(),
  push: z.union([ z.number(),z.number().array() ]).optional(),
}).strict();

export const EnumNodeTypeFieldUpdateOperationsInputSchema: z.ZodType<Prisma.EnumNodeTypeFieldUpdateOperationsInput> = z.object({
  set: z.lazy(() => NodeTypeSchema).optional()
}).strict();

export const EnumSourceTypeFieldUpdateOperationsInputSchema: z.ZodType<Prisma.EnumSourceTypeFieldUpdateOperationsInput> = z.object({
  set: z.lazy(() => SourceTypeSchema).optional()
}).strict();

export const BoolFieldUpdateOperationsInputSchema: z.ZodType<Prisma.BoolFieldUpdateOperationsInput> = z.object({
  set: z.boolean().optional()
}).strict();

export const UserUpdateOneRequiredWithoutNodesNestedInputSchema: z.ZodType<Prisma.UserUpdateOneRequiredWithoutNodesNestedInput> = z.object({
  create: z.union([ z.lazy(() => UserCreateWithoutNodesInputSchema),z.lazy(() => UserUncheckedCreateWithoutNodesInputSchema) ]).optional(),
  connectOrCreate: z.lazy(() => UserCreateOrConnectWithoutNodesInputSchema).optional(),
  upsert: z.lazy(() => UserUpsertWithoutNodesInputSchema).optional(),
  connect: z.lazy(() => UserWhereUniqueInputSchema).optional(),
  update: z.union([ z.lazy(() => UserUpdateToOneWithWhereWithoutNodesInputSchema),z.lazy(() => UserUpdateWithoutNodesInputSchema),z.lazy(() => UserUncheckedUpdateWithoutNodesInputSchema) ]).optional(),
}).strict();

export const NodeRelationshipUpdateManyWithoutFromNestedInputSchema: z.ZodType<Prisma.NodeRelationshipUpdateManyWithoutFromNestedInput> = z.object({
  create: z.union([ z.lazy(() => NodeRelationshipCreateWithoutFromInputSchema),z.lazy(() => NodeRelationshipCreateWithoutFromInputSchema).array(),z.lazy(() => NodeRelationshipUncheckedCreateWithoutFromInputSchema),z.lazy(() => NodeRelationshipUncheckedCreateWithoutFromInputSchema).array() ]).optional(),
  connectOrCreate: z.union([ z.lazy(() => NodeRelationshipCreateOrConnectWithoutFromInputSchema),z.lazy(() => NodeRelationshipCreateOrConnectWithoutFromInputSchema).array() ]).optional(),
  upsert: z.union([ z.lazy(() => NodeRelationshipUpsertWithWhereUniqueWithoutFromInputSchema),z.lazy(() => NodeRelationshipUpsertWithWhereUniqueWithoutFromInputSchema).array() ]).optional(),
  createMany: z.lazy(() => NodeRelationshipCreateManyFromInputEnvelopeSchema).optional(),
  set: z.union([ z.lazy(() => NodeRelationshipWhereUniqueInputSchema),z.lazy(() => NodeRelationshipWhereUniqueInputSchema).array() ]).optional(),
  disconnect: z.union([ z.lazy(() => NodeRelationshipWhereUniqueInputSchema),z.lazy(() => NodeRelationshipWhereUniqueInputSchema).array() ]).optional(),
  delete: z.union([ z.lazy(() => NodeRelationshipWhereUniqueInputSchema),z.lazy(() => NodeRelationshipWhereUniqueInputSchema).array() ]).optional(),
  connect: z.union([ z.lazy(() => NodeRelationshipWhereUniqueInputSchema),z.lazy(() => NodeRelationshipWhereUniqueInputSchema).array() ]).optional(),
  update: z.union([ z.lazy(() => NodeRelationshipUpdateWithWhereUniqueWithoutFromInputSchema),z.lazy(() => NodeRelationshipUpdateWithWhereUniqueWithoutFromInputSchema).array() ]).optional(),
  updateMany: z.union([ z.lazy(() => NodeRelationshipUpdateManyWithWhereWithoutFromInputSchema),z.lazy(() => NodeRelationshipUpdateManyWithWhereWithoutFromInputSchema).array() ]).optional(),
  deleteMany: z.union([ z.lazy(() => NodeRelationshipScalarWhereInputSchema),z.lazy(() => NodeRelationshipScalarWhereInputSchema).array() ]).optional(),
}).strict();

export const NodeRelationshipUpdateManyWithoutToNestedInputSchema: z.ZodType<Prisma.NodeRelationshipUpdateManyWithoutToNestedInput> = z.object({
  create: z.union([ z.lazy(() => NodeRelationshipCreateWithoutToInputSchema),z.lazy(() => NodeRelationshipCreateWithoutToInputSchema).array(),z.lazy(() => NodeRelationshipUncheckedCreateWithoutToInputSchema),z.lazy(() => NodeRelationshipUncheckedCreateWithoutToInputSchema).array() ]).optional(),
  connectOrCreate: z.union([ z.lazy(() => NodeRelationshipCreateOrConnectWithoutToInputSchema),z.lazy(() => NodeRelationshipCreateOrConnectWithoutToInputSchema).array() ]).optional(),
  upsert: z.union([ z.lazy(() => NodeRelationshipUpsertWithWhereUniqueWithoutToInputSchema),z.lazy(() => NodeRelationshipUpsertWithWhereUniqueWithoutToInputSchema).array() ]).optional(),
  createMany: z.lazy(() => NodeRelationshipCreateManyToInputEnvelopeSchema).optional(),
  set: z.union([ z.lazy(() => NodeRelationshipWhereUniqueInputSchema),z.lazy(() => NodeRelationshipWhereUniqueInputSchema).array() ]).optional(),
  disconnect: z.union([ z.lazy(() => NodeRelationshipWhereUniqueInputSchema),z.lazy(() => NodeRelationshipWhereUniqueInputSchema).array() ]).optional(),
  delete: z.union([ z.lazy(() => NodeRelationshipWhereUniqueInputSchema),z.lazy(() => NodeRelationshipWhereUniqueInputSchema).array() ]).optional(),
  connect: z.union([ z.lazy(() => NodeRelationshipWhereUniqueInputSchema),z.lazy(() => NodeRelationshipWhereUniqueInputSchema).array() ]).optional(),
  update: z.union([ z.lazy(() => NodeRelationshipUpdateWithWhereUniqueWithoutToInputSchema),z.lazy(() => NodeRelationshipUpdateWithWhereUniqueWithoutToInputSchema).array() ]).optional(),
  updateMany: z.union([ z.lazy(() => NodeRelationshipUpdateManyWithWhereWithoutToInputSchema),z.lazy(() => NodeRelationshipUpdateManyWithWhereWithoutToInputSchema).array() ]).optional(),
  deleteMany: z.union([ z.lazy(() => NodeRelationshipScalarWhereInputSchema),z.lazy(() => NodeRelationshipScalarWhereInputSchema).array() ]).optional(),
}).strict();

export const ValidationUpdateManyWithoutNodeNestedInputSchema: z.ZodType<Prisma.ValidationUpdateManyWithoutNodeNestedInput> = z.object({
  create: z.union([ z.lazy(() => ValidationCreateWithoutNodeInputSchema),z.lazy(() => ValidationCreateWithoutNodeInputSchema).array(),z.lazy(() => ValidationUncheckedCreateWithoutNodeInputSchema),z.lazy(() => ValidationUncheckedCreateWithoutNodeInputSchema).array() ]).optional(),
  connectOrCreate: z.union([ z.lazy(() => ValidationCreateOrConnectWithoutNodeInputSchema),z.lazy(() => ValidationCreateOrConnectWithoutNodeInputSchema).array() ]).optional(),
  upsert: z.union([ z.lazy(() => ValidationUpsertWithWhereUniqueWithoutNodeInputSchema),z.lazy(() => ValidationUpsertWithWhereUniqueWithoutNodeInputSchema).array() ]).optional(),
  createMany: z.lazy(() => ValidationCreateManyNodeInputEnvelopeSchema).optional(),
  set: z.union([ z.lazy(() => ValidationWhereUniqueInputSchema),z.lazy(() => ValidationWhereUniqueInputSchema).array() ]).optional(),
  disconnect: z.union([ z.lazy(() => ValidationWhereUniqueInputSchema),z.lazy(() => ValidationWhereUniqueInputSchema).array() ]).optional(),
  delete: z.union([ z.lazy(() => ValidationWhereUniqueInputSchema),z.lazy(() => ValidationWhereUniqueInputSchema).array() ]).optional(),
  connect: z.union([ z.lazy(() => ValidationWhereUniqueInputSchema),z.lazy(() => ValidationWhereUniqueInputSchema).array() ]).optional(),
  update: z.union([ z.lazy(() => ValidationUpdateWithWhereUniqueWithoutNodeInputSchema),z.lazy(() => ValidationUpdateWithWhereUniqueWithoutNodeInputSchema).array() ]).optional(),
  updateMany: z.union([ z.lazy(() => ValidationUpdateManyWithWhereWithoutNodeInputSchema),z.lazy(() => ValidationUpdateManyWithWhereWithoutNodeInputSchema).array() ]).optional(),
  deleteMany: z.union([ z.lazy(() => ValidationScalarWhereInputSchema),z.lazy(() => ValidationScalarWhereInputSchema).array() ]).optional(),
}).strict();

export const TopicUpdateManyWithoutNodesNestedInputSchema: z.ZodType<Prisma.TopicUpdateManyWithoutNodesNestedInput> = z.object({
  create: z.union([ z.lazy(() => TopicCreateWithoutNodesInputSchema),z.lazy(() => TopicCreateWithoutNodesInputSchema).array(),z.lazy(() => TopicUncheckedCreateWithoutNodesInputSchema),z.lazy(() => TopicUncheckedCreateWithoutNodesInputSchema).array() ]).optional(),
  connectOrCreate: z.union([ z.lazy(() => TopicCreateOrConnectWithoutNodesInputSchema),z.lazy(() => TopicCreateOrConnectWithoutNodesInputSchema).array() ]).optional(),
  upsert: z.union([ z.lazy(() => TopicUpsertWithWhereUniqueWithoutNodesInputSchema),z.lazy(() => TopicUpsertWithWhereUniqueWithoutNodesInputSchema).array() ]).optional(),
  set: z.union([ z.lazy(() => TopicWhereUniqueInputSchema),z.lazy(() => TopicWhereUniqueInputSchema).array() ]).optional(),
  disconnect: z.union([ z.lazy(() => TopicWhereUniqueInputSchema),z.lazy(() => TopicWhereUniqueInputSchema).array() ]).optional(),
  delete: z.union([ z.lazy(() => TopicWhereUniqueInputSchema),z.lazy(() => TopicWhereUniqueInputSchema).array() ]).optional(),
  connect: z.union([ z.lazy(() => TopicWhereUniqueInputSchema),z.lazy(() => TopicWhereUniqueInputSchema).array() ]).optional(),
  update: z.union([ z.lazy(() => TopicUpdateWithWhereUniqueWithoutNodesInputSchema),z.lazy(() => TopicUpdateWithWhereUniqueWithoutNodesInputSchema).array() ]).optional(),
  updateMany: z.union([ z.lazy(() => TopicUpdateManyWithWhereWithoutNodesInputSchema),z.lazy(() => TopicUpdateManyWithWhereWithoutNodesInputSchema).array() ]).optional(),
  deleteMany: z.union([ z.lazy(() => TopicScalarWhereInputSchema),z.lazy(() => TopicScalarWhereInputSchema).array() ]).optional(),
}).strict();

export const NodeRelationshipUncheckedUpdateManyWithoutFromNestedInputSchema: z.ZodType<Prisma.NodeRelationshipUncheckedUpdateManyWithoutFromNestedInput> = z.object({
  create: z.union([ z.lazy(() => NodeRelationshipCreateWithoutFromInputSchema),z.lazy(() => NodeRelationshipCreateWithoutFromInputSchema).array(),z.lazy(() => NodeRelationshipUncheckedCreateWithoutFromInputSchema),z.lazy(() => NodeRelationshipUncheckedCreateWithoutFromInputSchema).array() ]).optional(),
  connectOrCreate: z.union([ z.lazy(() => NodeRelationshipCreateOrConnectWithoutFromInputSchema),z.lazy(() => NodeRelationshipCreateOrConnectWithoutFromInputSchema).array() ]).optional(),
  upsert: z.union([ z.lazy(() => NodeRelationshipUpsertWithWhereUniqueWithoutFromInputSchema),z.lazy(() => NodeRelationshipUpsertWithWhereUniqueWithoutFromInputSchema).array() ]).optional(),
  createMany: z.lazy(() => NodeRelationshipCreateManyFromInputEnvelopeSchema).optional(),
  set: z.union([ z.lazy(() => NodeRelationshipWhereUniqueInputSchema),z.lazy(() => NodeRelationshipWhereUniqueInputSchema).array() ]).optional(),
  disconnect: z.union([ z.lazy(() => NodeRelationshipWhereUniqueInputSchema),z.lazy(() => NodeRelationshipWhereUniqueInputSchema).array() ]).optional(),
  delete: z.union([ z.lazy(() => NodeRelationshipWhereUniqueInputSchema),z.lazy(() => NodeRelationshipWhereUniqueInputSchema).array() ]).optional(),
  connect: z.union([ z.lazy(() => NodeRelationshipWhereUniqueInputSchema),z.lazy(() => NodeRelationshipWhereUniqueInputSchema).array() ]).optional(),
  update: z.union([ z.lazy(() => NodeRelationshipUpdateWithWhereUniqueWithoutFromInputSchema),z.lazy(() => NodeRelationshipUpdateWithWhereUniqueWithoutFromInputSchema).array() ]).optional(),
  updateMany: z.union([ z.lazy(() => NodeRelationshipUpdateManyWithWhereWithoutFromInputSchema),z.lazy(() => NodeRelationshipUpdateManyWithWhereWithoutFromInputSchema).array() ]).optional(),
  deleteMany: z.union([ z.lazy(() => NodeRelationshipScalarWhereInputSchema),z.lazy(() => NodeRelationshipScalarWhereInputSchema).array() ]).optional(),
}).strict();

export const NodeRelationshipUncheckedUpdateManyWithoutToNestedInputSchema: z.ZodType<Prisma.NodeRelationshipUncheckedUpdateManyWithoutToNestedInput> = z.object({
  create: z.union([ z.lazy(() => NodeRelationshipCreateWithoutToInputSchema),z.lazy(() => NodeRelationshipCreateWithoutToInputSchema).array(),z.lazy(() => NodeRelationshipUncheckedCreateWithoutToInputSchema),z.lazy(() => NodeRelationshipUncheckedCreateWithoutToInputSchema).array() ]).optional(),
  connectOrCreate: z.union([ z.lazy(() => NodeRelationshipCreateOrConnectWithoutToInputSchema),z.lazy(() => NodeRelationshipCreateOrConnectWithoutToInputSchema).array() ]).optional(),
  upsert: z.union([ z.lazy(() => NodeRelationshipUpsertWithWhereUniqueWithoutToInputSchema),z.lazy(() => NodeRelationshipUpsertWithWhereUniqueWithoutToInputSchema).array() ]).optional(),
  createMany: z.lazy(() => NodeRelationshipCreateManyToInputEnvelopeSchema).optional(),
  set: z.union([ z.lazy(() => NodeRelationshipWhereUniqueInputSchema),z.lazy(() => NodeRelationshipWhereUniqueInputSchema).array() ]).optional(),
  disconnect: z.union([ z.lazy(() => NodeRelationshipWhereUniqueInputSchema),z.lazy(() => NodeRelationshipWhereUniqueInputSchema).array() ]).optional(),
  delete: z.union([ z.lazy(() => NodeRelationshipWhereUniqueInputSchema),z.lazy(() => NodeRelationshipWhereUniqueInputSchema).array() ]).optional(),
  connect: z.union([ z.lazy(() => NodeRelationshipWhereUniqueInputSchema),z.lazy(() => NodeRelationshipWhereUniqueInputSchema).array() ]).optional(),
  update: z.union([ z.lazy(() => NodeRelationshipUpdateWithWhereUniqueWithoutToInputSchema),z.lazy(() => NodeRelationshipUpdateWithWhereUniqueWithoutToInputSchema).array() ]).optional(),
  updateMany: z.union([ z.lazy(() => NodeRelationshipUpdateManyWithWhereWithoutToInputSchema),z.lazy(() => NodeRelationshipUpdateManyWithWhereWithoutToInputSchema).array() ]).optional(),
  deleteMany: z.union([ z.lazy(() => NodeRelationshipScalarWhereInputSchema),z.lazy(() => NodeRelationshipScalarWhereInputSchema).array() ]).optional(),
}).strict();

export const ValidationUncheckedUpdateManyWithoutNodeNestedInputSchema: z.ZodType<Prisma.ValidationUncheckedUpdateManyWithoutNodeNestedInput> = z.object({
  create: z.union([ z.lazy(() => ValidationCreateWithoutNodeInputSchema),z.lazy(() => ValidationCreateWithoutNodeInputSchema).array(),z.lazy(() => ValidationUncheckedCreateWithoutNodeInputSchema),z.lazy(() => ValidationUncheckedCreateWithoutNodeInputSchema).array() ]).optional(),
  connectOrCreate: z.union([ z.lazy(() => ValidationCreateOrConnectWithoutNodeInputSchema),z.lazy(() => ValidationCreateOrConnectWithoutNodeInputSchema).array() ]).optional(),
  upsert: z.union([ z.lazy(() => ValidationUpsertWithWhereUniqueWithoutNodeInputSchema),z.lazy(() => ValidationUpsertWithWhereUniqueWithoutNodeInputSchema).array() ]).optional(),
  createMany: z.lazy(() => ValidationCreateManyNodeInputEnvelopeSchema).optional(),
  set: z.union([ z.lazy(() => ValidationWhereUniqueInputSchema),z.lazy(() => ValidationWhereUniqueInputSchema).array() ]).optional(),
  disconnect: z.union([ z.lazy(() => ValidationWhereUniqueInputSchema),z.lazy(() => ValidationWhereUniqueInputSchema).array() ]).optional(),
  delete: z.union([ z.lazy(() => ValidationWhereUniqueInputSchema),z.lazy(() => ValidationWhereUniqueInputSchema).array() ]).optional(),
  connect: z.union([ z.lazy(() => ValidationWhereUniqueInputSchema),z.lazy(() => ValidationWhereUniqueInputSchema).array() ]).optional(),
  update: z.union([ z.lazy(() => ValidationUpdateWithWhereUniqueWithoutNodeInputSchema),z.lazy(() => ValidationUpdateWithWhereUniqueWithoutNodeInputSchema).array() ]).optional(),
  updateMany: z.union([ z.lazy(() => ValidationUpdateManyWithWhereWithoutNodeInputSchema),z.lazy(() => ValidationUpdateManyWithWhereWithoutNodeInputSchema).array() ]).optional(),
  deleteMany: z.union([ z.lazy(() => ValidationScalarWhereInputSchema),z.lazy(() => ValidationScalarWhereInputSchema).array() ]).optional(),
}).strict();

export const TopicUncheckedUpdateManyWithoutNodesNestedInputSchema: z.ZodType<Prisma.TopicUncheckedUpdateManyWithoutNodesNestedInput> = z.object({
  create: z.union([ z.lazy(() => TopicCreateWithoutNodesInputSchema),z.lazy(() => TopicCreateWithoutNodesInputSchema).array(),z.lazy(() => TopicUncheckedCreateWithoutNodesInputSchema),z.lazy(() => TopicUncheckedCreateWithoutNodesInputSchema).array() ]).optional(),
  connectOrCreate: z.union([ z.lazy(() => TopicCreateOrConnectWithoutNodesInputSchema),z.lazy(() => TopicCreateOrConnectWithoutNodesInputSchema).array() ]).optional(),
  upsert: z.union([ z.lazy(() => TopicUpsertWithWhereUniqueWithoutNodesInputSchema),z.lazy(() => TopicUpsertWithWhereUniqueWithoutNodesInputSchema).array() ]).optional(),
  set: z.union([ z.lazy(() => TopicWhereUniqueInputSchema),z.lazy(() => TopicWhereUniqueInputSchema).array() ]).optional(),
  disconnect: z.union([ z.lazy(() => TopicWhereUniqueInputSchema),z.lazy(() => TopicWhereUniqueInputSchema).array() ]).optional(),
  delete: z.union([ z.lazy(() => TopicWhereUniqueInputSchema),z.lazy(() => TopicWhereUniqueInputSchema).array() ]).optional(),
  connect: z.union([ z.lazy(() => TopicWhereUniqueInputSchema),z.lazy(() => TopicWhereUniqueInputSchema).array() ]).optional(),
  update: z.union([ z.lazy(() => TopicUpdateWithWhereUniqueWithoutNodesInputSchema),z.lazy(() => TopicUpdateWithWhereUniqueWithoutNodesInputSchema).array() ]).optional(),
  updateMany: z.union([ z.lazy(() => TopicUpdateManyWithWhereWithoutNodesInputSchema),z.lazy(() => TopicUpdateManyWithWhereWithoutNodesInputSchema).array() ]).optional(),
  deleteMany: z.union([ z.lazy(() => TopicScalarWhereInputSchema),z.lazy(() => TopicScalarWhereInputSchema).array() ]).optional(),
}).strict();

export const KnowledgeNodeCreateNestedOneWithoutRelationshipsFromInputSchema: z.ZodType<Prisma.KnowledgeNodeCreateNestedOneWithoutRelationshipsFromInput> = z.object({
  create: z.union([ z.lazy(() => KnowledgeNodeCreateWithoutRelationshipsFromInputSchema),z.lazy(() => KnowledgeNodeUncheckedCreateWithoutRelationshipsFromInputSchema) ]).optional(),
  connectOrCreate: z.lazy(() => KnowledgeNodeCreateOrConnectWithoutRelationshipsFromInputSchema).optional(),
  connect: z.lazy(() => KnowledgeNodeWhereUniqueInputSchema).optional()
}).strict();

export const KnowledgeNodeCreateNestedOneWithoutRelationshipsToInputSchema: z.ZodType<Prisma.KnowledgeNodeCreateNestedOneWithoutRelationshipsToInput> = z.object({
  create: z.union([ z.lazy(() => KnowledgeNodeCreateWithoutRelationshipsToInputSchema),z.lazy(() => KnowledgeNodeUncheckedCreateWithoutRelationshipsToInputSchema) ]).optional(),
  connectOrCreate: z.lazy(() => KnowledgeNodeCreateOrConnectWithoutRelationshipsToInputSchema).optional(),
  connect: z.lazy(() => KnowledgeNodeWhereUniqueInputSchema).optional()
}).strict();

export const EnumRelationshipTypeFieldUpdateOperationsInputSchema: z.ZodType<Prisma.EnumRelationshipTypeFieldUpdateOperationsInput> = z.object({
  set: z.lazy(() => RelationshipTypeSchema).optional()
}).strict();

export const KnowledgeNodeUpdateOneRequiredWithoutRelationshipsFromNestedInputSchema: z.ZodType<Prisma.KnowledgeNodeUpdateOneRequiredWithoutRelationshipsFromNestedInput> = z.object({
  create: z.union([ z.lazy(() => KnowledgeNodeCreateWithoutRelationshipsFromInputSchema),z.lazy(() => KnowledgeNodeUncheckedCreateWithoutRelationshipsFromInputSchema) ]).optional(),
  connectOrCreate: z.lazy(() => KnowledgeNodeCreateOrConnectWithoutRelationshipsFromInputSchema).optional(),
  upsert: z.lazy(() => KnowledgeNodeUpsertWithoutRelationshipsFromInputSchema).optional(),
  connect: z.lazy(() => KnowledgeNodeWhereUniqueInputSchema).optional(),
  update: z.union([ z.lazy(() => KnowledgeNodeUpdateToOneWithWhereWithoutRelationshipsFromInputSchema),z.lazy(() => KnowledgeNodeUpdateWithoutRelationshipsFromInputSchema),z.lazy(() => KnowledgeNodeUncheckedUpdateWithoutRelationshipsFromInputSchema) ]).optional(),
}).strict();

export const KnowledgeNodeUpdateOneRequiredWithoutRelationshipsToNestedInputSchema: z.ZodType<Prisma.KnowledgeNodeUpdateOneRequiredWithoutRelationshipsToNestedInput> = z.object({
  create: z.union([ z.lazy(() => KnowledgeNodeCreateWithoutRelationshipsToInputSchema),z.lazy(() => KnowledgeNodeUncheckedCreateWithoutRelationshipsToInputSchema) ]).optional(),
  connectOrCreate: z.lazy(() => KnowledgeNodeCreateOrConnectWithoutRelationshipsToInputSchema).optional(),
  upsert: z.lazy(() => KnowledgeNodeUpsertWithoutRelationshipsToInputSchema).optional(),
  connect: z.lazy(() => KnowledgeNodeWhereUniqueInputSchema).optional(),
  update: z.union([ z.lazy(() => KnowledgeNodeUpdateToOneWithWhereWithoutRelationshipsToInputSchema),z.lazy(() => KnowledgeNodeUpdateWithoutRelationshipsToInputSchema),z.lazy(() => KnowledgeNodeUncheckedUpdateWithoutRelationshipsToInputSchema) ]).optional(),
}).strict();

export const KnowledgeNodeCreateNestedOneWithoutValidationsInputSchema: z.ZodType<Prisma.KnowledgeNodeCreateNestedOneWithoutValidationsInput> = z.object({
  create: z.union([ z.lazy(() => KnowledgeNodeCreateWithoutValidationsInputSchema),z.lazy(() => KnowledgeNodeUncheckedCreateWithoutValidationsInputSchema) ]).optional(),
  connectOrCreate: z.lazy(() => KnowledgeNodeCreateOrConnectWithoutValidationsInputSchema).optional(),
  connect: z.lazy(() => KnowledgeNodeWhereUniqueInputSchema).optional()
}).strict();

export const UserCreateNestedOneWithoutValidationsInputSchema: z.ZodType<Prisma.UserCreateNestedOneWithoutValidationsInput> = z.object({
  create: z.union([ z.lazy(() => UserCreateWithoutValidationsInputSchema),z.lazy(() => UserUncheckedCreateWithoutValidationsInputSchema) ]).optional(),
  connectOrCreate: z.lazy(() => UserCreateOrConnectWithoutValidationsInputSchema).optional(),
  connect: z.lazy(() => UserWhereUniqueInputSchema).optional()
}).strict();

export const KnowledgeNodeUpdateOneRequiredWithoutValidationsNestedInputSchema: z.ZodType<Prisma.KnowledgeNodeUpdateOneRequiredWithoutValidationsNestedInput> = z.object({
  create: z.union([ z.lazy(() => KnowledgeNodeCreateWithoutValidationsInputSchema),z.lazy(() => KnowledgeNodeUncheckedCreateWithoutValidationsInputSchema) ]).optional(),
  connectOrCreate: z.lazy(() => KnowledgeNodeCreateOrConnectWithoutValidationsInputSchema).optional(),
  upsert: z.lazy(() => KnowledgeNodeUpsertWithoutValidationsInputSchema).optional(),
  connect: z.lazy(() => KnowledgeNodeWhereUniqueInputSchema).optional(),
  update: z.union([ z.lazy(() => KnowledgeNodeUpdateToOneWithWhereWithoutValidationsInputSchema),z.lazy(() => KnowledgeNodeUpdateWithoutValidationsInputSchema),z.lazy(() => KnowledgeNodeUncheckedUpdateWithoutValidationsInputSchema) ]).optional(),
}).strict();

export const UserUpdateOneRequiredWithoutValidationsNestedInputSchema: z.ZodType<Prisma.UserUpdateOneRequiredWithoutValidationsNestedInput> = z.object({
  create: z.union([ z.lazy(() => UserCreateWithoutValidationsInputSchema),z.lazy(() => UserUncheckedCreateWithoutValidationsInputSchema) ]).optional(),
  connectOrCreate: z.lazy(() => UserCreateOrConnectWithoutValidationsInputSchema).optional(),
  upsert: z.lazy(() => UserUpsertWithoutValidationsInputSchema).optional(),
  connect: z.lazy(() => UserWhereUniqueInputSchema).optional(),
  update: z.union([ z.lazy(() => UserUpdateToOneWithWhereWithoutValidationsInputSchema),z.lazy(() => UserUpdateWithoutValidationsInputSchema),z.lazy(() => UserUncheckedUpdateWithoutValidationsInputSchema) ]).optional(),
}).strict();

export const KnowledgeNodeCreateNestedManyWithoutTopicsInputSchema: z.ZodType<Prisma.KnowledgeNodeCreateNestedManyWithoutTopicsInput> = z.object({
  create: z.union([ z.lazy(() => KnowledgeNodeCreateWithoutTopicsInputSchema),z.lazy(() => KnowledgeNodeCreateWithoutTopicsInputSchema).array(),z.lazy(() => KnowledgeNodeUncheckedCreateWithoutTopicsInputSchema),z.lazy(() => KnowledgeNodeUncheckedCreateWithoutTopicsInputSchema).array() ]).optional(),
  connectOrCreate: z.union([ z.lazy(() => KnowledgeNodeCreateOrConnectWithoutTopicsInputSchema),z.lazy(() => KnowledgeNodeCreateOrConnectWithoutTopicsInputSchema).array() ]).optional(),
  connect: z.union([ z.lazy(() => KnowledgeNodeWhereUniqueInputSchema),z.lazy(() => KnowledgeNodeWhereUniqueInputSchema).array() ]).optional(),
}).strict();

export const KnowledgeNodeUncheckedCreateNestedManyWithoutTopicsInputSchema: z.ZodType<Prisma.KnowledgeNodeUncheckedCreateNestedManyWithoutTopicsInput> = z.object({
  create: z.union([ z.lazy(() => KnowledgeNodeCreateWithoutTopicsInputSchema),z.lazy(() => KnowledgeNodeCreateWithoutTopicsInputSchema).array(),z.lazy(() => KnowledgeNodeUncheckedCreateWithoutTopicsInputSchema),z.lazy(() => KnowledgeNodeUncheckedCreateWithoutTopicsInputSchema).array() ]).optional(),
  connectOrCreate: z.union([ z.lazy(() => KnowledgeNodeCreateOrConnectWithoutTopicsInputSchema),z.lazy(() => KnowledgeNodeCreateOrConnectWithoutTopicsInputSchema).array() ]).optional(),
  connect: z.union([ z.lazy(() => KnowledgeNodeWhereUniqueInputSchema),z.lazy(() => KnowledgeNodeWhereUniqueInputSchema).array() ]).optional(),
}).strict();

export const KnowledgeNodeUpdateManyWithoutTopicsNestedInputSchema: z.ZodType<Prisma.KnowledgeNodeUpdateManyWithoutTopicsNestedInput> = z.object({
  create: z.union([ z.lazy(() => KnowledgeNodeCreateWithoutTopicsInputSchema),z.lazy(() => KnowledgeNodeCreateWithoutTopicsInputSchema).array(),z.lazy(() => KnowledgeNodeUncheckedCreateWithoutTopicsInputSchema),z.lazy(() => KnowledgeNodeUncheckedCreateWithoutTopicsInputSchema).array() ]).optional(),
  connectOrCreate: z.union([ z.lazy(() => KnowledgeNodeCreateOrConnectWithoutTopicsInputSchema),z.lazy(() => KnowledgeNodeCreateOrConnectWithoutTopicsInputSchema).array() ]).optional(),
  upsert: z.union([ z.lazy(() => KnowledgeNodeUpsertWithWhereUniqueWithoutTopicsInputSchema),z.lazy(() => KnowledgeNodeUpsertWithWhereUniqueWithoutTopicsInputSchema).array() ]).optional(),
  set: z.union([ z.lazy(() => KnowledgeNodeWhereUniqueInputSchema),z.lazy(() => KnowledgeNodeWhereUniqueInputSchema).array() ]).optional(),
  disconnect: z.union([ z.lazy(() => KnowledgeNodeWhereUniqueInputSchema),z.lazy(() => KnowledgeNodeWhereUniqueInputSchema).array() ]).optional(),
  delete: z.union([ z.lazy(() => KnowledgeNodeWhereUniqueInputSchema),z.lazy(() => KnowledgeNodeWhereUniqueInputSchema).array() ]).optional(),
  connect: z.union([ z.lazy(() => KnowledgeNodeWhereUniqueInputSchema),z.lazy(() => KnowledgeNodeWhereUniqueInputSchema).array() ]).optional(),
  update: z.union([ z.lazy(() => KnowledgeNodeUpdateWithWhereUniqueWithoutTopicsInputSchema),z.lazy(() => KnowledgeNodeUpdateWithWhereUniqueWithoutTopicsInputSchema).array() ]).optional(),
  updateMany: z.union([ z.lazy(() => KnowledgeNodeUpdateManyWithWhereWithoutTopicsInputSchema),z.lazy(() => KnowledgeNodeUpdateManyWithWhereWithoutTopicsInputSchema).array() ]).optional(),
  deleteMany: z.union([ z.lazy(() => KnowledgeNodeScalarWhereInputSchema),z.lazy(() => KnowledgeNodeScalarWhereInputSchema).array() ]).optional(),
}).strict();

export const KnowledgeNodeUncheckedUpdateManyWithoutTopicsNestedInputSchema: z.ZodType<Prisma.KnowledgeNodeUncheckedUpdateManyWithoutTopicsNestedInput> = z.object({
  create: z.union([ z.lazy(() => KnowledgeNodeCreateWithoutTopicsInputSchema),z.lazy(() => KnowledgeNodeCreateWithoutTopicsInputSchema).array(),z.lazy(() => KnowledgeNodeUncheckedCreateWithoutTopicsInputSchema),z.lazy(() => KnowledgeNodeUncheckedCreateWithoutTopicsInputSchema).array() ]).optional(),
  connectOrCreate: z.union([ z.lazy(() => KnowledgeNodeCreateOrConnectWithoutTopicsInputSchema),z.lazy(() => KnowledgeNodeCreateOrConnectWithoutTopicsInputSchema).array() ]).optional(),
  upsert: z.union([ z.lazy(() => KnowledgeNodeUpsertWithWhereUniqueWithoutTopicsInputSchema),z.lazy(() => KnowledgeNodeUpsertWithWhereUniqueWithoutTopicsInputSchema).array() ]).optional(),
  set: z.union([ z.lazy(() => KnowledgeNodeWhereUniqueInputSchema),z.lazy(() => KnowledgeNodeWhereUniqueInputSchema).array() ]).optional(),
  disconnect: z.union([ z.lazy(() => KnowledgeNodeWhereUniqueInputSchema),z.lazy(() => KnowledgeNodeWhereUniqueInputSchema).array() ]).optional(),
  delete: z.union([ z.lazy(() => KnowledgeNodeWhereUniqueInputSchema),z.lazy(() => KnowledgeNodeWhereUniqueInputSchema).array() ]).optional(),
  connect: z.union([ z.lazy(() => KnowledgeNodeWhereUniqueInputSchema),z.lazy(() => KnowledgeNodeWhereUniqueInputSchema).array() ]).optional(),
  update: z.union([ z.lazy(() => KnowledgeNodeUpdateWithWhereUniqueWithoutTopicsInputSchema),z.lazy(() => KnowledgeNodeUpdateWithWhereUniqueWithoutTopicsInputSchema).array() ]).optional(),
  updateMany: z.union([ z.lazy(() => KnowledgeNodeUpdateManyWithWhereWithoutTopicsInputSchema),z.lazy(() => KnowledgeNodeUpdateManyWithWhereWithoutTopicsInputSchema).array() ]).optional(),
  deleteMany: z.union([ z.lazy(() => KnowledgeNodeScalarWhereInputSchema),z.lazy(() => KnowledgeNodeScalarWhereInputSchema).array() ]).optional(),
}).strict();

export const NestedIntFilterSchema: z.ZodType<Prisma.NestedIntFilter> = z.object({
  equals: z.number().optional(),
  in: z.number().array().optional(),
  notIn: z.number().array().optional(),
  lt: z.number().optional(),
  lte: z.number().optional(),
  gt: z.number().optional(),
  gte: z.number().optional(),
  not: z.union([ z.number(),z.lazy(() => NestedIntFilterSchema) ]).optional(),
}).strict();

export const NestedStringFilterSchema: z.ZodType<Prisma.NestedStringFilter> = z.object({
  equals: z.string().optional(),
  in: z.string().array().optional(),
  notIn: z.string().array().optional(),
  lt: z.string().optional(),
  lte: z.string().optional(),
  gt: z.string().optional(),
  gte: z.string().optional(),
  contains: z.string().optional(),
  startsWith: z.string().optional(),
  endsWith: z.string().optional(),
  not: z.union([ z.string(),z.lazy(() => NestedStringFilterSchema) ]).optional(),
}).strict();

export const NestedIntWithAggregatesFilterSchema: z.ZodType<Prisma.NestedIntWithAggregatesFilter> = z.object({
  equals: z.number().optional(),
  in: z.number().array().optional(),
  notIn: z.number().array().optional(),
  lt: z.number().optional(),
  lte: z.number().optional(),
  gt: z.number().optional(),
  gte: z.number().optional(),
  not: z.union([ z.number(),z.lazy(() => NestedIntWithAggregatesFilterSchema) ]).optional(),
  _count: z.lazy(() => NestedIntFilterSchema).optional(),
  _avg: z.lazy(() => NestedFloatFilterSchema).optional(),
  _sum: z.lazy(() => NestedIntFilterSchema).optional(),
  _min: z.lazy(() => NestedIntFilterSchema).optional(),
  _max: z.lazy(() => NestedIntFilterSchema).optional()
}).strict();

export const NestedFloatFilterSchema: z.ZodType<Prisma.NestedFloatFilter> = z.object({
  equals: z.number().optional(),
  in: z.number().array().optional(),
  notIn: z.number().array().optional(),
  lt: z.number().optional(),
  lte: z.number().optional(),
  gt: z.number().optional(),
  gte: z.number().optional(),
  not: z.union([ z.number(),z.lazy(() => NestedFloatFilterSchema) ]).optional(),
}).strict();

export const NestedStringWithAggregatesFilterSchema: z.ZodType<Prisma.NestedStringWithAggregatesFilter> = z.object({
  equals: z.string().optional(),
  in: z.string().array().optional(),
  notIn: z.string().array().optional(),
  lt: z.string().optional(),
  lte: z.string().optional(),
  gt: z.string().optional(),
  gte: z.string().optional(),
  contains: z.string().optional(),
  startsWith: z.string().optional(),
  endsWith: z.string().optional(),
  not: z.union([ z.string(),z.lazy(() => NestedStringWithAggregatesFilterSchema) ]).optional(),
  _count: z.lazy(() => NestedIntFilterSchema).optional(),
  _min: z.lazy(() => NestedStringFilterSchema).optional(),
  _max: z.lazy(() => NestedStringFilterSchema).optional()
}).strict();

export const NestedStringNullableFilterSchema: z.ZodType<Prisma.NestedStringNullableFilter> = z.object({
  equals: z.string().optional().nullable(),
  in: z.string().array().optional().nullable(),
  notIn: z.string().array().optional().nullable(),
  lt: z.string().optional(),
  lte: z.string().optional(),
  gt: z.string().optional(),
  gte: z.string().optional(),
  contains: z.string().optional(),
  startsWith: z.string().optional(),
  endsWith: z.string().optional(),
  not: z.union([ z.string(),z.lazy(() => NestedStringNullableFilterSchema) ]).optional().nullable(),
}).strict();

export const NestedDateTimeFilterSchema: z.ZodType<Prisma.NestedDateTimeFilter> = z.object({
  equals: z.coerce.date().optional(),
  in: z.coerce.date().array().optional(),
  notIn: z.coerce.date().array().optional(),
  lt: z.coerce.date().optional(),
  lte: z.coerce.date().optional(),
  gt: z.coerce.date().optional(),
  gte: z.coerce.date().optional(),
  not: z.union([ z.coerce.date(),z.lazy(() => NestedDateTimeFilterSchema) ]).optional(),
}).strict();

export const NestedStringNullableWithAggregatesFilterSchema: z.ZodType<Prisma.NestedStringNullableWithAggregatesFilter> = z.object({
  equals: z.string().optional().nullable(),
  in: z.string().array().optional().nullable(),
  notIn: z.string().array().optional().nullable(),
  lt: z.string().optional(),
  lte: z.string().optional(),
  gt: z.string().optional(),
  gte: z.string().optional(),
  contains: z.string().optional(),
  startsWith: z.string().optional(),
  endsWith: z.string().optional(),
  not: z.union([ z.string(),z.lazy(() => NestedStringNullableWithAggregatesFilterSchema) ]).optional().nullable(),
  _count: z.lazy(() => NestedIntNullableFilterSchema).optional(),
  _min: z.lazy(() => NestedStringNullableFilterSchema).optional(),
  _max: z.lazy(() => NestedStringNullableFilterSchema).optional()
}).strict();

export const NestedIntNullableFilterSchema: z.ZodType<Prisma.NestedIntNullableFilter> = z.object({
  equals: z.number().optional().nullable(),
  in: z.number().array().optional().nullable(),
  notIn: z.number().array().optional().nullable(),
  lt: z.number().optional(),
  lte: z.number().optional(),
  gt: z.number().optional(),
  gte: z.number().optional(),
  not: z.union([ z.number(),z.lazy(() => NestedIntNullableFilterSchema) ]).optional().nullable(),
}).strict();

export const NestedJsonNullableFilterSchema: z.ZodType<Prisma.NestedJsonNullableFilter> = z.object({
  equals: InputJsonValueSchema.optional(),
  path: z.string().array().optional(),
  mode: z.lazy(() => QueryModeSchema).optional(),
  string_contains: z.string().optional(),
  string_starts_with: z.string().optional(),
  string_ends_with: z.string().optional(),
  array_starts_with: InputJsonValueSchema.optional().nullable(),
  array_ends_with: InputJsonValueSchema.optional().nullable(),
  array_contains: InputJsonValueSchema.optional().nullable(),
  lt: InputJsonValueSchema.optional(),
  lte: InputJsonValueSchema.optional(),
  gt: InputJsonValueSchema.optional(),
  gte: InputJsonValueSchema.optional(),
  not: InputJsonValueSchema.optional()
}).strict();

export const NestedDateTimeWithAggregatesFilterSchema: z.ZodType<Prisma.NestedDateTimeWithAggregatesFilter> = z.object({
  equals: z.coerce.date().optional(),
  in: z.coerce.date().array().optional(),
  notIn: z.coerce.date().array().optional(),
  lt: z.coerce.date().optional(),
  lte: z.coerce.date().optional(),
  gt: z.coerce.date().optional(),
  gte: z.coerce.date().optional(),
  not: z.union([ z.coerce.date(),z.lazy(() => NestedDateTimeWithAggregatesFilterSchema) ]).optional(),
  _count: z.lazy(() => NestedIntFilterSchema).optional(),
  _min: z.lazy(() => NestedDateTimeFilterSchema).optional(),
  _max: z.lazy(() => NestedDateTimeFilterSchema).optional()
}).strict();

export const NestedEnumNodeTypeFilterSchema: z.ZodType<Prisma.NestedEnumNodeTypeFilter> = z.object({
  equals: z.lazy(() => NodeTypeSchema).optional(),
  in: z.lazy(() => NodeTypeSchema).array().optional(),
  notIn: z.lazy(() => NodeTypeSchema).array().optional(),
  not: z.union([ z.lazy(() => NodeTypeSchema),z.lazy(() => NestedEnumNodeTypeFilterSchema) ]).optional(),
}).strict();

export const NestedEnumSourceTypeFilterSchema: z.ZodType<Prisma.NestedEnumSourceTypeFilter> = z.object({
  equals: z.lazy(() => SourceTypeSchema).optional(),
  in: z.lazy(() => SourceTypeSchema).array().optional(),
  notIn: z.lazy(() => SourceTypeSchema).array().optional(),
  not: z.union([ z.lazy(() => SourceTypeSchema),z.lazy(() => NestedEnumSourceTypeFilterSchema) ]).optional(),
}).strict();

export const NestedBoolFilterSchema: z.ZodType<Prisma.NestedBoolFilter> = z.object({
  equals: z.boolean().optional(),
  not: z.union([ z.boolean(),z.lazy(() => NestedBoolFilterSchema) ]).optional(),
}).strict();

export const NestedEnumNodeTypeWithAggregatesFilterSchema: z.ZodType<Prisma.NestedEnumNodeTypeWithAggregatesFilter> = z.object({
  equals: z.lazy(() => NodeTypeSchema).optional(),
  in: z.lazy(() => NodeTypeSchema).array().optional(),
  notIn: z.lazy(() => NodeTypeSchema).array().optional(),
  not: z.union([ z.lazy(() => NodeTypeSchema),z.lazy(() => NestedEnumNodeTypeWithAggregatesFilterSchema) ]).optional(),
  _count: z.lazy(() => NestedIntFilterSchema).optional(),
  _min: z.lazy(() => NestedEnumNodeTypeFilterSchema).optional(),
  _max: z.lazy(() => NestedEnumNodeTypeFilterSchema).optional()
}).strict();

export const NestedEnumSourceTypeWithAggregatesFilterSchema: z.ZodType<Prisma.NestedEnumSourceTypeWithAggregatesFilter> = z.object({
  equals: z.lazy(() => SourceTypeSchema).optional(),
  in: z.lazy(() => SourceTypeSchema).array().optional(),
  notIn: z.lazy(() => SourceTypeSchema).array().optional(),
  not: z.union([ z.lazy(() => SourceTypeSchema),z.lazy(() => NestedEnumSourceTypeWithAggregatesFilterSchema) ]).optional(),
  _count: z.lazy(() => NestedIntFilterSchema).optional(),
  _min: z.lazy(() => NestedEnumSourceTypeFilterSchema).optional(),
  _max: z.lazy(() => NestedEnumSourceTypeFilterSchema).optional()
}).strict();

export const NestedBoolWithAggregatesFilterSchema: z.ZodType<Prisma.NestedBoolWithAggregatesFilter> = z.object({
  equals: z.boolean().optional(),
  not: z.union([ z.boolean(),z.lazy(() => NestedBoolWithAggregatesFilterSchema) ]).optional(),
  _count: z.lazy(() => NestedIntFilterSchema).optional(),
  _min: z.lazy(() => NestedBoolFilterSchema).optional(),
  _max: z.lazy(() => NestedBoolFilterSchema).optional()
}).strict();

export const NestedEnumRelationshipTypeFilterSchema: z.ZodType<Prisma.NestedEnumRelationshipTypeFilter> = z.object({
  equals: z.lazy(() => RelationshipTypeSchema).optional(),
  in: z.lazy(() => RelationshipTypeSchema).array().optional(),
  notIn: z.lazy(() => RelationshipTypeSchema).array().optional(),
  not: z.union([ z.lazy(() => RelationshipTypeSchema),z.lazy(() => NestedEnumRelationshipTypeFilterSchema) ]).optional(),
}).strict();

export const NestedEnumRelationshipTypeWithAggregatesFilterSchema: z.ZodType<Prisma.NestedEnumRelationshipTypeWithAggregatesFilter> = z.object({
  equals: z.lazy(() => RelationshipTypeSchema).optional(),
  in: z.lazy(() => RelationshipTypeSchema).array().optional(),
  notIn: z.lazy(() => RelationshipTypeSchema).array().optional(),
  not: z.union([ z.lazy(() => RelationshipTypeSchema),z.lazy(() => NestedEnumRelationshipTypeWithAggregatesFilterSchema) ]).optional(),
  _count: z.lazy(() => NestedIntFilterSchema).optional(),
  _min: z.lazy(() => NestedEnumRelationshipTypeFilterSchema).optional(),
  _max: z.lazy(() => NestedEnumRelationshipTypeFilterSchema).optional()
}).strict();

export const KnowledgeNodeCreateWithoutAuthorInputSchema: z.ZodType<Prisma.KnowledgeNodeCreateWithoutAuthorInput> = z.object({
  id: z.string().cuid().optional(),
  content: z.string(),
  embedding: z.union([ z.lazy(() => KnowledgeNodeCreateembeddingInputSchema),z.number().array() ]).optional(),
  type: z.lazy(() => NodeTypeSchema),
  source: z.lazy(() => SourceTypeSchema),
  context: z.union([ z.lazy(() => NullableJsonNullValueInputSchema),InputJsonValueSchema ]).optional(),
  validated: z.boolean().optional(),
  createdAt: z.coerce.date().optional(),
  updatedAt: z.coerce.date().optional(),
  relationshipsFrom: z.lazy(() => NodeRelationshipCreateNestedManyWithoutFromInputSchema).optional(),
  relationshipsTo: z.lazy(() => NodeRelationshipCreateNestedManyWithoutToInputSchema).optional(),
  validations: z.lazy(() => ValidationCreateNestedManyWithoutNodeInputSchema).optional(),
  topics: z.lazy(() => TopicCreateNestedManyWithoutNodesInputSchema).optional()
}).strict();

export const KnowledgeNodeUncheckedCreateWithoutAuthorInputSchema: z.ZodType<Prisma.KnowledgeNodeUncheckedCreateWithoutAuthorInput> = z.object({
  id: z.string().cuid().optional(),
  content: z.string(),
  embedding: z.union([ z.lazy(() => KnowledgeNodeCreateembeddingInputSchema),z.number().array() ]).optional(),
  type: z.lazy(() => NodeTypeSchema),
  source: z.lazy(() => SourceTypeSchema),
  context: z.union([ z.lazy(() => NullableJsonNullValueInputSchema),InputJsonValueSchema ]).optional(),
  validated: z.boolean().optional(),
  createdAt: z.coerce.date().optional(),
  updatedAt: z.coerce.date().optional(),
  relationshipsFrom: z.lazy(() => NodeRelationshipUncheckedCreateNestedManyWithoutFromInputSchema).optional(),
  relationshipsTo: z.lazy(() => NodeRelationshipUncheckedCreateNestedManyWithoutToInputSchema).optional(),
  validations: z.lazy(() => ValidationUncheckedCreateNestedManyWithoutNodeInputSchema).optional(),
  topics: z.lazy(() => TopicUncheckedCreateNestedManyWithoutNodesInputSchema).optional()
}).strict();

export const KnowledgeNodeCreateOrConnectWithoutAuthorInputSchema: z.ZodType<Prisma.KnowledgeNodeCreateOrConnectWithoutAuthorInput> = z.object({
  where: z.lazy(() => KnowledgeNodeWhereUniqueInputSchema),
  create: z.union([ z.lazy(() => KnowledgeNodeCreateWithoutAuthorInputSchema),z.lazy(() => KnowledgeNodeUncheckedCreateWithoutAuthorInputSchema) ]),
}).strict();

export const KnowledgeNodeCreateManyAuthorInputEnvelopeSchema: z.ZodType<Prisma.KnowledgeNodeCreateManyAuthorInputEnvelope> = z.object({
  data: z.union([ z.lazy(() => KnowledgeNodeCreateManyAuthorInputSchema),z.lazy(() => KnowledgeNodeCreateManyAuthorInputSchema).array() ]),
  skipDuplicates: z.boolean().optional()
}).strict();

export const ValidationCreateWithoutExpertInputSchema: z.ZodType<Prisma.ValidationCreateWithoutExpertInput> = z.object({
  id: z.string().cuid().optional(),
  notes: z.string().optional().nullable(),
  createdAt: z.coerce.date().optional(),
  updatedAt: z.coerce.date().optional(),
  node: z.lazy(() => KnowledgeNodeCreateNestedOneWithoutValidationsInputSchema)
}).strict();

export const ValidationUncheckedCreateWithoutExpertInputSchema: z.ZodType<Prisma.ValidationUncheckedCreateWithoutExpertInput> = z.object({
  id: z.string().cuid().optional(),
  nodeId: z.string(),
  notes: z.string().optional().nullable(),
  createdAt: z.coerce.date().optional(),
  updatedAt: z.coerce.date().optional()
}).strict();

export const ValidationCreateOrConnectWithoutExpertInputSchema: z.ZodType<Prisma.ValidationCreateOrConnectWithoutExpertInput> = z.object({
  where: z.lazy(() => ValidationWhereUniqueInputSchema),
  create: z.union([ z.lazy(() => ValidationCreateWithoutExpertInputSchema),z.lazy(() => ValidationUncheckedCreateWithoutExpertInputSchema) ]),
}).strict();

export const ValidationCreateManyExpertInputEnvelopeSchema: z.ZodType<Prisma.ValidationCreateManyExpertInputEnvelope> = z.object({
  data: z.union([ z.lazy(() => ValidationCreateManyExpertInputSchema),z.lazy(() => ValidationCreateManyExpertInputSchema).array() ]),
  skipDuplicates: z.boolean().optional()
}).strict();

export const KnowledgeNodeUpsertWithWhereUniqueWithoutAuthorInputSchema: z.ZodType<Prisma.KnowledgeNodeUpsertWithWhereUniqueWithoutAuthorInput> = z.object({
  where: z.lazy(() => KnowledgeNodeWhereUniqueInputSchema),
  update: z.union([ z.lazy(() => KnowledgeNodeUpdateWithoutAuthorInputSchema),z.lazy(() => KnowledgeNodeUncheckedUpdateWithoutAuthorInputSchema) ]),
  create: z.union([ z.lazy(() => KnowledgeNodeCreateWithoutAuthorInputSchema),z.lazy(() => KnowledgeNodeUncheckedCreateWithoutAuthorInputSchema) ]),
}).strict();

export const KnowledgeNodeUpdateWithWhereUniqueWithoutAuthorInputSchema: z.ZodType<Prisma.KnowledgeNodeUpdateWithWhereUniqueWithoutAuthorInput> = z.object({
  where: z.lazy(() => KnowledgeNodeWhereUniqueInputSchema),
  data: z.union([ z.lazy(() => KnowledgeNodeUpdateWithoutAuthorInputSchema),z.lazy(() => KnowledgeNodeUncheckedUpdateWithoutAuthorInputSchema) ]),
}).strict();

export const KnowledgeNodeUpdateManyWithWhereWithoutAuthorInputSchema: z.ZodType<Prisma.KnowledgeNodeUpdateManyWithWhereWithoutAuthorInput> = z.object({
  where: z.lazy(() => KnowledgeNodeScalarWhereInputSchema),
  data: z.union([ z.lazy(() => KnowledgeNodeUpdateManyMutationInputSchema),z.lazy(() => KnowledgeNodeUncheckedUpdateManyWithoutAuthorInputSchema) ]),
}).strict();

export const KnowledgeNodeScalarWhereInputSchema: z.ZodType<Prisma.KnowledgeNodeScalarWhereInput> = z.object({
  AND: z.union([ z.lazy(() => KnowledgeNodeScalarWhereInputSchema),z.lazy(() => KnowledgeNodeScalarWhereInputSchema).array() ]).optional(),
  OR: z.lazy(() => KnowledgeNodeScalarWhereInputSchema).array().optional(),
  NOT: z.union([ z.lazy(() => KnowledgeNodeScalarWhereInputSchema),z.lazy(() => KnowledgeNodeScalarWhereInputSchema).array() ]).optional(),
  id: z.union([ z.lazy(() => StringFilterSchema),z.string() ]).optional(),
  content: z.union([ z.lazy(() => StringFilterSchema),z.string() ]).optional(),
  embedding: z.lazy(() => FloatNullableListFilterSchema).optional(),
  type: z.union([ z.lazy(() => EnumNodeTypeFilterSchema),z.lazy(() => NodeTypeSchema) ]).optional(),
  source: z.union([ z.lazy(() => EnumSourceTypeFilterSchema),z.lazy(() => SourceTypeSchema) ]).optional(),
  context: z.lazy(() => JsonNullableFilterSchema).optional(),
  validated: z.union([ z.lazy(() => BoolFilterSchema),z.boolean() ]).optional(),
  createdAt: z.union([ z.lazy(() => DateTimeFilterSchema),z.coerce.date() ]).optional(),
  updatedAt: z.union([ z.lazy(() => DateTimeFilterSchema),z.coerce.date() ]).optional(),
  authorId: z.union([ z.lazy(() => StringFilterSchema),z.string() ]).optional(),
}).strict();

export const ValidationUpsertWithWhereUniqueWithoutExpertInputSchema: z.ZodType<Prisma.ValidationUpsertWithWhereUniqueWithoutExpertInput> = z.object({
  where: z.lazy(() => ValidationWhereUniqueInputSchema),
  update: z.union([ z.lazy(() => ValidationUpdateWithoutExpertInputSchema),z.lazy(() => ValidationUncheckedUpdateWithoutExpertInputSchema) ]),
  create: z.union([ z.lazy(() => ValidationCreateWithoutExpertInputSchema),z.lazy(() => ValidationUncheckedCreateWithoutExpertInputSchema) ]),
}).strict();

export const ValidationUpdateWithWhereUniqueWithoutExpertInputSchema: z.ZodType<Prisma.ValidationUpdateWithWhereUniqueWithoutExpertInput> = z.object({
  where: z.lazy(() => ValidationWhereUniqueInputSchema),
  data: z.union([ z.lazy(() => ValidationUpdateWithoutExpertInputSchema),z.lazy(() => ValidationUncheckedUpdateWithoutExpertInputSchema) ]),
}).strict();

export const ValidationUpdateManyWithWhereWithoutExpertInputSchema: z.ZodType<Prisma.ValidationUpdateManyWithWhereWithoutExpertInput> = z.object({
  where: z.lazy(() => ValidationScalarWhereInputSchema),
  data: z.union([ z.lazy(() => ValidationUpdateManyMutationInputSchema),z.lazy(() => ValidationUncheckedUpdateManyWithoutExpertInputSchema) ]),
}).strict();

export const ValidationScalarWhereInputSchema: z.ZodType<Prisma.ValidationScalarWhereInput> = z.object({
  AND: z.union([ z.lazy(() => ValidationScalarWhereInputSchema),z.lazy(() => ValidationScalarWhereInputSchema).array() ]).optional(),
  OR: z.lazy(() => ValidationScalarWhereInputSchema).array().optional(),
  NOT: z.union([ z.lazy(() => ValidationScalarWhereInputSchema),z.lazy(() => ValidationScalarWhereInputSchema).array() ]).optional(),
  id: z.union([ z.lazy(() => StringFilterSchema),z.string() ]).optional(),
  nodeId: z.union([ z.lazy(() => StringFilterSchema),z.string() ]).optional(),
  expertId: z.union([ z.lazy(() => StringFilterSchema),z.string() ]).optional(),
  notes: z.union([ z.lazy(() => StringNullableFilterSchema),z.string() ]).optional().nullable(),
  createdAt: z.union([ z.lazy(() => DateTimeFilterSchema),z.coerce.date() ]).optional(),
  updatedAt: z.union([ z.lazy(() => DateTimeFilterSchema),z.coerce.date() ]).optional(),
}).strict();

export const UserCreateWithoutNodesInputSchema: z.ZodType<Prisma.UserCreateWithoutNodesInput> = z.object({
  userId: z.string(),
  validations: z.lazy(() => ValidationCreateNestedManyWithoutExpertInputSchema).optional()
}).strict();

export const UserUncheckedCreateWithoutNodesInputSchema: z.ZodType<Prisma.UserUncheckedCreateWithoutNodesInput> = z.object({
  userId: z.string(),
  validations: z.lazy(() => ValidationUncheckedCreateNestedManyWithoutExpertInputSchema).optional()
}).strict();

export const UserCreateOrConnectWithoutNodesInputSchema: z.ZodType<Prisma.UserCreateOrConnectWithoutNodesInput> = z.object({
  where: z.lazy(() => UserWhereUniqueInputSchema),
  create: z.union([ z.lazy(() => UserCreateWithoutNodesInputSchema),z.lazy(() => UserUncheckedCreateWithoutNodesInputSchema) ]),
}).strict();

export const NodeRelationshipCreateWithoutFromInputSchema: z.ZodType<Prisma.NodeRelationshipCreateWithoutFromInput> = z.object({
  id: z.string().cuid().optional(),
  type: z.lazy(() => RelationshipTypeSchema),
  createdAt: z.coerce.date().optional(),
  updatedAt: z.coerce.date().optional(),
  to: z.lazy(() => KnowledgeNodeCreateNestedOneWithoutRelationshipsToInputSchema)
}).strict();

export const NodeRelationshipUncheckedCreateWithoutFromInputSchema: z.ZodType<Prisma.NodeRelationshipUncheckedCreateWithoutFromInput> = z.object({
  id: z.string().cuid().optional(),
  toId: z.string(),
  type: z.lazy(() => RelationshipTypeSchema),
  createdAt: z.coerce.date().optional(),
  updatedAt: z.coerce.date().optional()
}).strict();

export const NodeRelationshipCreateOrConnectWithoutFromInputSchema: z.ZodType<Prisma.NodeRelationshipCreateOrConnectWithoutFromInput> = z.object({
  where: z.lazy(() => NodeRelationshipWhereUniqueInputSchema),
  create: z.union([ z.lazy(() => NodeRelationshipCreateWithoutFromInputSchema),z.lazy(() => NodeRelationshipUncheckedCreateWithoutFromInputSchema) ]),
}).strict();

export const NodeRelationshipCreateManyFromInputEnvelopeSchema: z.ZodType<Prisma.NodeRelationshipCreateManyFromInputEnvelope> = z.object({
  data: z.union([ z.lazy(() => NodeRelationshipCreateManyFromInputSchema),z.lazy(() => NodeRelationshipCreateManyFromInputSchema).array() ]),
  skipDuplicates: z.boolean().optional()
}).strict();

export const NodeRelationshipCreateWithoutToInputSchema: z.ZodType<Prisma.NodeRelationshipCreateWithoutToInput> = z.object({
  id: z.string().cuid().optional(),
  type: z.lazy(() => RelationshipTypeSchema),
  createdAt: z.coerce.date().optional(),
  updatedAt: z.coerce.date().optional(),
  from: z.lazy(() => KnowledgeNodeCreateNestedOneWithoutRelationshipsFromInputSchema)
}).strict();

export const NodeRelationshipUncheckedCreateWithoutToInputSchema: z.ZodType<Prisma.NodeRelationshipUncheckedCreateWithoutToInput> = z.object({
  id: z.string().cuid().optional(),
  fromId: z.string(),
  type: z.lazy(() => RelationshipTypeSchema),
  createdAt: z.coerce.date().optional(),
  updatedAt: z.coerce.date().optional()
}).strict();

export const NodeRelationshipCreateOrConnectWithoutToInputSchema: z.ZodType<Prisma.NodeRelationshipCreateOrConnectWithoutToInput> = z.object({
  where: z.lazy(() => NodeRelationshipWhereUniqueInputSchema),
  create: z.union([ z.lazy(() => NodeRelationshipCreateWithoutToInputSchema),z.lazy(() => NodeRelationshipUncheckedCreateWithoutToInputSchema) ]),
}).strict();

export const NodeRelationshipCreateManyToInputEnvelopeSchema: z.ZodType<Prisma.NodeRelationshipCreateManyToInputEnvelope> = z.object({
  data: z.union([ z.lazy(() => NodeRelationshipCreateManyToInputSchema),z.lazy(() => NodeRelationshipCreateManyToInputSchema).array() ]),
  skipDuplicates: z.boolean().optional()
}).strict();

export const ValidationCreateWithoutNodeInputSchema: z.ZodType<Prisma.ValidationCreateWithoutNodeInput> = z.object({
  id: z.string().cuid().optional(),
  notes: z.string().optional().nullable(),
  createdAt: z.coerce.date().optional(),
  updatedAt: z.coerce.date().optional(),
  expert: z.lazy(() => UserCreateNestedOneWithoutValidationsInputSchema)
}).strict();

export const ValidationUncheckedCreateWithoutNodeInputSchema: z.ZodType<Prisma.ValidationUncheckedCreateWithoutNodeInput> = z.object({
  id: z.string().cuid().optional(),
  expertId: z.string(),
  notes: z.string().optional().nullable(),
  createdAt: z.coerce.date().optional(),
  updatedAt: z.coerce.date().optional()
}).strict();

export const ValidationCreateOrConnectWithoutNodeInputSchema: z.ZodType<Prisma.ValidationCreateOrConnectWithoutNodeInput> = z.object({
  where: z.lazy(() => ValidationWhereUniqueInputSchema),
  create: z.union([ z.lazy(() => ValidationCreateWithoutNodeInputSchema),z.lazy(() => ValidationUncheckedCreateWithoutNodeInputSchema) ]),
}).strict();

export const ValidationCreateManyNodeInputEnvelopeSchema: z.ZodType<Prisma.ValidationCreateManyNodeInputEnvelope> = z.object({
  data: z.union([ z.lazy(() => ValidationCreateManyNodeInputSchema),z.lazy(() => ValidationCreateManyNodeInputSchema).array() ]),
  skipDuplicates: z.boolean().optional()
}).strict();

export const TopicCreateWithoutNodesInputSchema: z.ZodType<Prisma.TopicCreateWithoutNodesInput> = z.object({
  id: z.string().cuid().optional(),
  name: z.string(),
  createdAt: z.coerce.date().optional(),
  updatedAt: z.coerce.date().optional()
}).strict();

export const TopicUncheckedCreateWithoutNodesInputSchema: z.ZodType<Prisma.TopicUncheckedCreateWithoutNodesInput> = z.object({
  id: z.string().cuid().optional(),
  name: z.string(),
  createdAt: z.coerce.date().optional(),
  updatedAt: z.coerce.date().optional()
}).strict();

export const TopicCreateOrConnectWithoutNodesInputSchema: z.ZodType<Prisma.TopicCreateOrConnectWithoutNodesInput> = z.object({
  where: z.lazy(() => TopicWhereUniqueInputSchema),
  create: z.union([ z.lazy(() => TopicCreateWithoutNodesInputSchema),z.lazy(() => TopicUncheckedCreateWithoutNodesInputSchema) ]),
}).strict();

export const UserUpsertWithoutNodesInputSchema: z.ZodType<Prisma.UserUpsertWithoutNodesInput> = z.object({
  update: z.union([ z.lazy(() => UserUpdateWithoutNodesInputSchema),z.lazy(() => UserUncheckedUpdateWithoutNodesInputSchema) ]),
  create: z.union([ z.lazy(() => UserCreateWithoutNodesInputSchema),z.lazy(() => UserUncheckedCreateWithoutNodesInputSchema) ]),
  where: z.lazy(() => UserWhereInputSchema).optional()
}).strict();

export const UserUpdateToOneWithWhereWithoutNodesInputSchema: z.ZodType<Prisma.UserUpdateToOneWithWhereWithoutNodesInput> = z.object({
  where: z.lazy(() => UserWhereInputSchema).optional(),
  data: z.union([ z.lazy(() => UserUpdateWithoutNodesInputSchema),z.lazy(() => UserUncheckedUpdateWithoutNodesInputSchema) ]),
}).strict();

export const UserUpdateWithoutNodesInputSchema: z.ZodType<Prisma.UserUpdateWithoutNodesInput> = z.object({
  userId: z.union([ z.string(),z.lazy(() => StringFieldUpdateOperationsInputSchema) ]).optional(),
  validations: z.lazy(() => ValidationUpdateManyWithoutExpertNestedInputSchema).optional()
}).strict();

export const UserUncheckedUpdateWithoutNodesInputSchema: z.ZodType<Prisma.UserUncheckedUpdateWithoutNodesInput> = z.object({
  userId: z.union([ z.string(),z.lazy(() => StringFieldUpdateOperationsInputSchema) ]).optional(),
  validations: z.lazy(() => ValidationUncheckedUpdateManyWithoutExpertNestedInputSchema).optional()
}).strict();

export const NodeRelationshipUpsertWithWhereUniqueWithoutFromInputSchema: z.ZodType<Prisma.NodeRelationshipUpsertWithWhereUniqueWithoutFromInput> = z.object({
  where: z.lazy(() => NodeRelationshipWhereUniqueInputSchema),
  update: z.union([ z.lazy(() => NodeRelationshipUpdateWithoutFromInputSchema),z.lazy(() => NodeRelationshipUncheckedUpdateWithoutFromInputSchema) ]),
  create: z.union([ z.lazy(() => NodeRelationshipCreateWithoutFromInputSchema),z.lazy(() => NodeRelationshipUncheckedCreateWithoutFromInputSchema) ]),
}).strict();

export const NodeRelationshipUpdateWithWhereUniqueWithoutFromInputSchema: z.ZodType<Prisma.NodeRelationshipUpdateWithWhereUniqueWithoutFromInput> = z.object({
  where: z.lazy(() => NodeRelationshipWhereUniqueInputSchema),
  data: z.union([ z.lazy(() => NodeRelationshipUpdateWithoutFromInputSchema),z.lazy(() => NodeRelationshipUncheckedUpdateWithoutFromInputSchema) ]),
}).strict();

export const NodeRelationshipUpdateManyWithWhereWithoutFromInputSchema: z.ZodType<Prisma.NodeRelationshipUpdateManyWithWhereWithoutFromInput> = z.object({
  where: z.lazy(() => NodeRelationshipScalarWhereInputSchema),
  data: z.union([ z.lazy(() => NodeRelationshipUpdateManyMutationInputSchema),z.lazy(() => NodeRelationshipUncheckedUpdateManyWithoutFromInputSchema) ]),
}).strict();

export const NodeRelationshipScalarWhereInputSchema: z.ZodType<Prisma.NodeRelationshipScalarWhereInput> = z.object({
  AND: z.union([ z.lazy(() => NodeRelationshipScalarWhereInputSchema),z.lazy(() => NodeRelationshipScalarWhereInputSchema).array() ]).optional(),
  OR: z.lazy(() => NodeRelationshipScalarWhereInputSchema).array().optional(),
  NOT: z.union([ z.lazy(() => NodeRelationshipScalarWhereInputSchema),z.lazy(() => NodeRelationshipScalarWhereInputSchema).array() ]).optional(),
  id: z.union([ z.lazy(() => StringFilterSchema),z.string() ]).optional(),
  fromId: z.union([ z.lazy(() => StringFilterSchema),z.string() ]).optional(),
  toId: z.union([ z.lazy(() => StringFilterSchema),z.string() ]).optional(),
  type: z.union([ z.lazy(() => EnumRelationshipTypeFilterSchema),z.lazy(() => RelationshipTypeSchema) ]).optional(),
  createdAt: z.union([ z.lazy(() => DateTimeFilterSchema),z.coerce.date() ]).optional(),
  updatedAt: z.union([ z.lazy(() => DateTimeFilterSchema),z.coerce.date() ]).optional(),
}).strict();

export const NodeRelationshipUpsertWithWhereUniqueWithoutToInputSchema: z.ZodType<Prisma.NodeRelationshipUpsertWithWhereUniqueWithoutToInput> = z.object({
  where: z.lazy(() => NodeRelationshipWhereUniqueInputSchema),
  update: z.union([ z.lazy(() => NodeRelationshipUpdateWithoutToInputSchema),z.lazy(() => NodeRelationshipUncheckedUpdateWithoutToInputSchema) ]),
  create: z.union([ z.lazy(() => NodeRelationshipCreateWithoutToInputSchema),z.lazy(() => NodeRelationshipUncheckedCreateWithoutToInputSchema) ]),
}).strict();

export const NodeRelationshipUpdateWithWhereUniqueWithoutToInputSchema: z.ZodType<Prisma.NodeRelationshipUpdateWithWhereUniqueWithoutToInput> = z.object({
  where: z.lazy(() => NodeRelationshipWhereUniqueInputSchema),
  data: z.union([ z.lazy(() => NodeRelationshipUpdateWithoutToInputSchema),z.lazy(() => NodeRelationshipUncheckedUpdateWithoutToInputSchema) ]),
}).strict();

export const NodeRelationshipUpdateManyWithWhereWithoutToInputSchema: z.ZodType<Prisma.NodeRelationshipUpdateManyWithWhereWithoutToInput> = z.object({
  where: z.lazy(() => NodeRelationshipScalarWhereInputSchema),
  data: z.union([ z.lazy(() => NodeRelationshipUpdateManyMutationInputSchema),z.lazy(() => NodeRelationshipUncheckedUpdateManyWithoutToInputSchema) ]),
}).strict();

export const ValidationUpsertWithWhereUniqueWithoutNodeInputSchema: z.ZodType<Prisma.ValidationUpsertWithWhereUniqueWithoutNodeInput> = z.object({
  where: z.lazy(() => ValidationWhereUniqueInputSchema),
  update: z.union([ z.lazy(() => ValidationUpdateWithoutNodeInputSchema),z.lazy(() => ValidationUncheckedUpdateWithoutNodeInputSchema) ]),
  create: z.union([ z.lazy(() => ValidationCreateWithoutNodeInputSchema),z.lazy(() => ValidationUncheckedCreateWithoutNodeInputSchema) ]),
}).strict();

export const ValidationUpdateWithWhereUniqueWithoutNodeInputSchema: z.ZodType<Prisma.ValidationUpdateWithWhereUniqueWithoutNodeInput> = z.object({
  where: z.lazy(() => ValidationWhereUniqueInputSchema),
  data: z.union([ z.lazy(() => ValidationUpdateWithoutNodeInputSchema),z.lazy(() => ValidationUncheckedUpdateWithoutNodeInputSchema) ]),
}).strict();

export const ValidationUpdateManyWithWhereWithoutNodeInputSchema: z.ZodType<Prisma.ValidationUpdateManyWithWhereWithoutNodeInput> = z.object({
  where: z.lazy(() => ValidationScalarWhereInputSchema),
  data: z.union([ z.lazy(() => ValidationUpdateManyMutationInputSchema),z.lazy(() => ValidationUncheckedUpdateManyWithoutNodeInputSchema) ]),
}).strict();

export const TopicUpsertWithWhereUniqueWithoutNodesInputSchema: z.ZodType<Prisma.TopicUpsertWithWhereUniqueWithoutNodesInput> = z.object({
  where: z.lazy(() => TopicWhereUniqueInputSchema),
  update: z.union([ z.lazy(() => TopicUpdateWithoutNodesInputSchema),z.lazy(() => TopicUncheckedUpdateWithoutNodesInputSchema) ]),
  create: z.union([ z.lazy(() => TopicCreateWithoutNodesInputSchema),z.lazy(() => TopicUncheckedCreateWithoutNodesInputSchema) ]),
}).strict();

export const TopicUpdateWithWhereUniqueWithoutNodesInputSchema: z.ZodType<Prisma.TopicUpdateWithWhereUniqueWithoutNodesInput> = z.object({
  where: z.lazy(() => TopicWhereUniqueInputSchema),
  data: z.union([ z.lazy(() => TopicUpdateWithoutNodesInputSchema),z.lazy(() => TopicUncheckedUpdateWithoutNodesInputSchema) ]),
}).strict();

export const TopicUpdateManyWithWhereWithoutNodesInputSchema: z.ZodType<Prisma.TopicUpdateManyWithWhereWithoutNodesInput> = z.object({
  where: z.lazy(() => TopicScalarWhereInputSchema),
  data: z.union([ z.lazy(() => TopicUpdateManyMutationInputSchema),z.lazy(() => TopicUncheckedUpdateManyWithoutNodesInputSchema) ]),
}).strict();

export const TopicScalarWhereInputSchema: z.ZodType<Prisma.TopicScalarWhereInput> = z.object({
  AND: z.union([ z.lazy(() => TopicScalarWhereInputSchema),z.lazy(() => TopicScalarWhereInputSchema).array() ]).optional(),
  OR: z.lazy(() => TopicScalarWhereInputSchema).array().optional(),
  NOT: z.union([ z.lazy(() => TopicScalarWhereInputSchema),z.lazy(() => TopicScalarWhereInputSchema).array() ]).optional(),
  id: z.union([ z.lazy(() => StringFilterSchema),z.string() ]).optional(),
  name: z.union([ z.lazy(() => StringFilterSchema),z.string() ]).optional(),
  createdAt: z.union([ z.lazy(() => DateTimeFilterSchema),z.coerce.date() ]).optional(),
  updatedAt: z.union([ z.lazy(() => DateTimeFilterSchema),z.coerce.date() ]).optional(),
}).strict();

export const KnowledgeNodeCreateWithoutRelationshipsFromInputSchema: z.ZodType<Prisma.KnowledgeNodeCreateWithoutRelationshipsFromInput> = z.object({
  id: z.string().cuid().optional(),
  content: z.string(),
  embedding: z.union([ z.lazy(() => KnowledgeNodeCreateembeddingInputSchema),z.number().array() ]).optional(),
  type: z.lazy(() => NodeTypeSchema),
  source: z.lazy(() => SourceTypeSchema),
  context: z.union([ z.lazy(() => NullableJsonNullValueInputSchema),InputJsonValueSchema ]).optional(),
  validated: z.boolean().optional(),
  createdAt: z.coerce.date().optional(),
  updatedAt: z.coerce.date().optional(),
  author: z.lazy(() => UserCreateNestedOneWithoutNodesInputSchema),
  relationshipsTo: z.lazy(() => NodeRelationshipCreateNestedManyWithoutToInputSchema).optional(),
  validations: z.lazy(() => ValidationCreateNestedManyWithoutNodeInputSchema).optional(),
  topics: z.lazy(() => TopicCreateNestedManyWithoutNodesInputSchema).optional()
}).strict();

export const KnowledgeNodeUncheckedCreateWithoutRelationshipsFromInputSchema: z.ZodType<Prisma.KnowledgeNodeUncheckedCreateWithoutRelationshipsFromInput> = z.object({
  id: z.string().cuid().optional(),
  content: z.string(),
  embedding: z.union([ z.lazy(() => KnowledgeNodeCreateembeddingInputSchema),z.number().array() ]).optional(),
  type: z.lazy(() => NodeTypeSchema),
  source: z.lazy(() => SourceTypeSchema),
  context: z.union([ z.lazy(() => NullableJsonNullValueInputSchema),InputJsonValueSchema ]).optional(),
  validated: z.boolean().optional(),
  createdAt: z.coerce.date().optional(),
  updatedAt: z.coerce.date().optional(),
  authorId: z.string(),
  relationshipsTo: z.lazy(() => NodeRelationshipUncheckedCreateNestedManyWithoutToInputSchema).optional(),
  validations: z.lazy(() => ValidationUncheckedCreateNestedManyWithoutNodeInputSchema).optional(),
  topics: z.lazy(() => TopicUncheckedCreateNestedManyWithoutNodesInputSchema).optional()
}).strict();

export const KnowledgeNodeCreateOrConnectWithoutRelationshipsFromInputSchema: z.ZodType<Prisma.KnowledgeNodeCreateOrConnectWithoutRelationshipsFromInput> = z.object({
  where: z.lazy(() => KnowledgeNodeWhereUniqueInputSchema),
  create: z.union([ z.lazy(() => KnowledgeNodeCreateWithoutRelationshipsFromInputSchema),z.lazy(() => KnowledgeNodeUncheckedCreateWithoutRelationshipsFromInputSchema) ]),
}).strict();

export const KnowledgeNodeCreateWithoutRelationshipsToInputSchema: z.ZodType<Prisma.KnowledgeNodeCreateWithoutRelationshipsToInput> = z.object({
  id: z.string().cuid().optional(),
  content: z.string(),
  embedding: z.union([ z.lazy(() => KnowledgeNodeCreateembeddingInputSchema),z.number().array() ]).optional(),
  type: z.lazy(() => NodeTypeSchema),
  source: z.lazy(() => SourceTypeSchema),
  context: z.union([ z.lazy(() => NullableJsonNullValueInputSchema),InputJsonValueSchema ]).optional(),
  validated: z.boolean().optional(),
  createdAt: z.coerce.date().optional(),
  updatedAt: z.coerce.date().optional(),
  author: z.lazy(() => UserCreateNestedOneWithoutNodesInputSchema),
  relationshipsFrom: z.lazy(() => NodeRelationshipCreateNestedManyWithoutFromInputSchema).optional(),
  validations: z.lazy(() => ValidationCreateNestedManyWithoutNodeInputSchema).optional(),
  topics: z.lazy(() => TopicCreateNestedManyWithoutNodesInputSchema).optional()
}).strict();

export const KnowledgeNodeUncheckedCreateWithoutRelationshipsToInputSchema: z.ZodType<Prisma.KnowledgeNodeUncheckedCreateWithoutRelationshipsToInput> = z.object({
  id: z.string().cuid().optional(),
  content: z.string(),
  embedding: z.union([ z.lazy(() => KnowledgeNodeCreateembeddingInputSchema),z.number().array() ]).optional(),
  type: z.lazy(() => NodeTypeSchema),
  source: z.lazy(() => SourceTypeSchema),
  context: z.union([ z.lazy(() => NullableJsonNullValueInputSchema),InputJsonValueSchema ]).optional(),
  validated: z.boolean().optional(),
  createdAt: z.coerce.date().optional(),
  updatedAt: z.coerce.date().optional(),
  authorId: z.string(),
  relationshipsFrom: z.lazy(() => NodeRelationshipUncheckedCreateNestedManyWithoutFromInputSchema).optional(),
  validations: z.lazy(() => ValidationUncheckedCreateNestedManyWithoutNodeInputSchema).optional(),
  topics: z.lazy(() => TopicUncheckedCreateNestedManyWithoutNodesInputSchema).optional()
}).strict();

export const KnowledgeNodeCreateOrConnectWithoutRelationshipsToInputSchema: z.ZodType<Prisma.KnowledgeNodeCreateOrConnectWithoutRelationshipsToInput> = z.object({
  where: z.lazy(() => KnowledgeNodeWhereUniqueInputSchema),
  create: z.union([ z.lazy(() => KnowledgeNodeCreateWithoutRelationshipsToInputSchema),z.lazy(() => KnowledgeNodeUncheckedCreateWithoutRelationshipsToInputSchema) ]),
}).strict();

export const KnowledgeNodeUpsertWithoutRelationshipsFromInputSchema: z.ZodType<Prisma.KnowledgeNodeUpsertWithoutRelationshipsFromInput> = z.object({
  update: z.union([ z.lazy(() => KnowledgeNodeUpdateWithoutRelationshipsFromInputSchema),z.lazy(() => KnowledgeNodeUncheckedUpdateWithoutRelationshipsFromInputSchema) ]),
  create: z.union([ z.lazy(() => KnowledgeNodeCreateWithoutRelationshipsFromInputSchema),z.lazy(() => KnowledgeNodeUncheckedCreateWithoutRelationshipsFromInputSchema) ]),
  where: z.lazy(() => KnowledgeNodeWhereInputSchema).optional()
}).strict();

export const KnowledgeNodeUpdateToOneWithWhereWithoutRelationshipsFromInputSchema: z.ZodType<Prisma.KnowledgeNodeUpdateToOneWithWhereWithoutRelationshipsFromInput> = z.object({
  where: z.lazy(() => KnowledgeNodeWhereInputSchema).optional(),
  data: z.union([ z.lazy(() => KnowledgeNodeUpdateWithoutRelationshipsFromInputSchema),z.lazy(() => KnowledgeNodeUncheckedUpdateWithoutRelationshipsFromInputSchema) ]),
}).strict();

export const KnowledgeNodeUpdateWithoutRelationshipsFromInputSchema: z.ZodType<Prisma.KnowledgeNodeUpdateWithoutRelationshipsFromInput> = z.object({
  id: z.union([ z.string().cuid(),z.lazy(() => StringFieldUpdateOperationsInputSchema) ]).optional(),
  content: z.union([ z.string(),z.lazy(() => StringFieldUpdateOperationsInputSchema) ]).optional(),
  embedding: z.union([ z.lazy(() => KnowledgeNodeUpdateembeddingInputSchema),z.number().array() ]).optional(),
  type: z.union([ z.lazy(() => NodeTypeSchema),z.lazy(() => EnumNodeTypeFieldUpdateOperationsInputSchema) ]).optional(),
  source: z.union([ z.lazy(() => SourceTypeSchema),z.lazy(() => EnumSourceTypeFieldUpdateOperationsInputSchema) ]).optional(),
  context: z.union([ z.lazy(() => NullableJsonNullValueInputSchema),InputJsonValueSchema ]).optional(),
  validated: z.union([ z.boolean(),z.lazy(() => BoolFieldUpdateOperationsInputSchema) ]).optional(),
  createdAt: z.union([ z.coerce.date(),z.lazy(() => DateTimeFieldUpdateOperationsInputSchema) ]).optional(),
  updatedAt: z.union([ z.coerce.date(),z.lazy(() => DateTimeFieldUpdateOperationsInputSchema) ]).optional(),
  author: z.lazy(() => UserUpdateOneRequiredWithoutNodesNestedInputSchema).optional(),
  relationshipsTo: z.lazy(() => NodeRelationshipUpdateManyWithoutToNestedInputSchema).optional(),
  validations: z.lazy(() => ValidationUpdateManyWithoutNodeNestedInputSchema).optional(),
  topics: z.lazy(() => TopicUpdateManyWithoutNodesNestedInputSchema).optional()
}).strict();

export const KnowledgeNodeUncheckedUpdateWithoutRelationshipsFromInputSchema: z.ZodType<Prisma.KnowledgeNodeUncheckedUpdateWithoutRelationshipsFromInput> = z.object({
  id: z.union([ z.string().cuid(),z.lazy(() => StringFieldUpdateOperationsInputSchema) ]).optional(),
  content: z.union([ z.string(),z.lazy(() => StringFieldUpdateOperationsInputSchema) ]).optional(),
  embedding: z.union([ z.lazy(() => KnowledgeNodeUpdateembeddingInputSchema),z.number().array() ]).optional(),
  type: z.union([ z.lazy(() => NodeTypeSchema),z.lazy(() => EnumNodeTypeFieldUpdateOperationsInputSchema) ]).optional(),
  source: z.union([ z.lazy(() => SourceTypeSchema),z.lazy(() => EnumSourceTypeFieldUpdateOperationsInputSchema) ]).optional(),
  context: z.union([ z.lazy(() => NullableJsonNullValueInputSchema),InputJsonValueSchema ]).optional(),
  validated: z.union([ z.boolean(),z.lazy(() => BoolFieldUpdateOperationsInputSchema) ]).optional(),
  createdAt: z.union([ z.coerce.date(),z.lazy(() => DateTimeFieldUpdateOperationsInputSchema) ]).optional(),
  updatedAt: z.union([ z.coerce.date(),z.lazy(() => DateTimeFieldUpdateOperationsInputSchema) ]).optional(),
  authorId: z.union([ z.string(),z.lazy(() => StringFieldUpdateOperationsInputSchema) ]).optional(),
  relationshipsTo: z.lazy(() => NodeRelationshipUncheckedUpdateManyWithoutToNestedInputSchema).optional(),
  validations: z.lazy(() => ValidationUncheckedUpdateManyWithoutNodeNestedInputSchema).optional(),
  topics: z.lazy(() => TopicUncheckedUpdateManyWithoutNodesNestedInputSchema).optional()
}).strict();

export const KnowledgeNodeUpsertWithoutRelationshipsToInputSchema: z.ZodType<Prisma.KnowledgeNodeUpsertWithoutRelationshipsToInput> = z.object({
  update: z.union([ z.lazy(() => KnowledgeNodeUpdateWithoutRelationshipsToInputSchema),z.lazy(() => KnowledgeNodeUncheckedUpdateWithoutRelationshipsToInputSchema) ]),
  create: z.union([ z.lazy(() => KnowledgeNodeCreateWithoutRelationshipsToInputSchema),z.lazy(() => KnowledgeNodeUncheckedCreateWithoutRelationshipsToInputSchema) ]),
  where: z.lazy(() => KnowledgeNodeWhereInputSchema).optional()
}).strict();

export const KnowledgeNodeUpdateToOneWithWhereWithoutRelationshipsToInputSchema: z.ZodType<Prisma.KnowledgeNodeUpdateToOneWithWhereWithoutRelationshipsToInput> = z.object({
  where: z.lazy(() => KnowledgeNodeWhereInputSchema).optional(),
  data: z.union([ z.lazy(() => KnowledgeNodeUpdateWithoutRelationshipsToInputSchema),z.lazy(() => KnowledgeNodeUncheckedUpdateWithoutRelationshipsToInputSchema) ]),
}).strict();

export const KnowledgeNodeUpdateWithoutRelationshipsToInputSchema: z.ZodType<Prisma.KnowledgeNodeUpdateWithoutRelationshipsToInput> = z.object({
  id: z.union([ z.string().cuid(),z.lazy(() => StringFieldUpdateOperationsInputSchema) ]).optional(),
  content: z.union([ z.string(),z.lazy(() => StringFieldUpdateOperationsInputSchema) ]).optional(),
  embedding: z.union([ z.lazy(() => KnowledgeNodeUpdateembeddingInputSchema),z.number().array() ]).optional(),
  type: z.union([ z.lazy(() => NodeTypeSchema),z.lazy(() => EnumNodeTypeFieldUpdateOperationsInputSchema) ]).optional(),
  source: z.union([ z.lazy(() => SourceTypeSchema),z.lazy(() => EnumSourceTypeFieldUpdateOperationsInputSchema) ]).optional(),
  context: z.union([ z.lazy(() => NullableJsonNullValueInputSchema),InputJsonValueSchema ]).optional(),
  validated: z.union([ z.boolean(),z.lazy(() => BoolFieldUpdateOperationsInputSchema) ]).optional(),
  createdAt: z.union([ z.coerce.date(),z.lazy(() => DateTimeFieldUpdateOperationsInputSchema) ]).optional(),
  updatedAt: z.union([ z.coerce.date(),z.lazy(() => DateTimeFieldUpdateOperationsInputSchema) ]).optional(),
  author: z.lazy(() => UserUpdateOneRequiredWithoutNodesNestedInputSchema).optional(),
  relationshipsFrom: z.lazy(() => NodeRelationshipUpdateManyWithoutFromNestedInputSchema).optional(),
  validations: z.lazy(() => ValidationUpdateManyWithoutNodeNestedInputSchema).optional(),
  topics: z.lazy(() => TopicUpdateManyWithoutNodesNestedInputSchema).optional()
}).strict();

export const KnowledgeNodeUncheckedUpdateWithoutRelationshipsToInputSchema: z.ZodType<Prisma.KnowledgeNodeUncheckedUpdateWithoutRelationshipsToInput> = z.object({
  id: z.union([ z.string().cuid(),z.lazy(() => StringFieldUpdateOperationsInputSchema) ]).optional(),
  content: z.union([ z.string(),z.lazy(() => StringFieldUpdateOperationsInputSchema) ]).optional(),
  embedding: z.union([ z.lazy(() => KnowledgeNodeUpdateembeddingInputSchema),z.number().array() ]).optional(),
  type: z.union([ z.lazy(() => NodeTypeSchema),z.lazy(() => EnumNodeTypeFieldUpdateOperationsInputSchema) ]).optional(),
  source: z.union([ z.lazy(() => SourceTypeSchema),z.lazy(() => EnumSourceTypeFieldUpdateOperationsInputSchema) ]).optional(),
  context: z.union([ z.lazy(() => NullableJsonNullValueInputSchema),InputJsonValueSchema ]).optional(),
  validated: z.union([ z.boolean(),z.lazy(() => BoolFieldUpdateOperationsInputSchema) ]).optional(),
  createdAt: z.union([ z.coerce.date(),z.lazy(() => DateTimeFieldUpdateOperationsInputSchema) ]).optional(),
  updatedAt: z.union([ z.coerce.date(),z.lazy(() => DateTimeFieldUpdateOperationsInputSchema) ]).optional(),
  authorId: z.union([ z.string(),z.lazy(() => StringFieldUpdateOperationsInputSchema) ]).optional(),
  relationshipsFrom: z.lazy(() => NodeRelationshipUncheckedUpdateManyWithoutFromNestedInputSchema).optional(),
  validations: z.lazy(() => ValidationUncheckedUpdateManyWithoutNodeNestedInputSchema).optional(),
  topics: z.lazy(() => TopicUncheckedUpdateManyWithoutNodesNestedInputSchema).optional()
}).strict();

export const KnowledgeNodeCreateWithoutValidationsInputSchema: z.ZodType<Prisma.KnowledgeNodeCreateWithoutValidationsInput> = z.object({
  id: z.string().cuid().optional(),
  content: z.string(),
  embedding: z.union([ z.lazy(() => KnowledgeNodeCreateembeddingInputSchema),z.number().array() ]).optional(),
  type: z.lazy(() => NodeTypeSchema),
  source: z.lazy(() => SourceTypeSchema),
  context: z.union([ z.lazy(() => NullableJsonNullValueInputSchema),InputJsonValueSchema ]).optional(),
  validated: z.boolean().optional(),
  createdAt: z.coerce.date().optional(),
  updatedAt: z.coerce.date().optional(),
  author: z.lazy(() => UserCreateNestedOneWithoutNodesInputSchema),
  relationshipsFrom: z.lazy(() => NodeRelationshipCreateNestedManyWithoutFromInputSchema).optional(),
  relationshipsTo: z.lazy(() => NodeRelationshipCreateNestedManyWithoutToInputSchema).optional(),
  topics: z.lazy(() => TopicCreateNestedManyWithoutNodesInputSchema).optional()
}).strict();

export const KnowledgeNodeUncheckedCreateWithoutValidationsInputSchema: z.ZodType<Prisma.KnowledgeNodeUncheckedCreateWithoutValidationsInput> = z.object({
  id: z.string().cuid().optional(),
  content: z.string(),
  embedding: z.union([ z.lazy(() => KnowledgeNodeCreateembeddingInputSchema),z.number().array() ]).optional(),
  type: z.lazy(() => NodeTypeSchema),
  source: z.lazy(() => SourceTypeSchema),
  context: z.union([ z.lazy(() => NullableJsonNullValueInputSchema),InputJsonValueSchema ]).optional(),
  validated: z.boolean().optional(),
  createdAt: z.coerce.date().optional(),
  updatedAt: z.coerce.date().optional(),
  authorId: z.string(),
  relationshipsFrom: z.lazy(() => NodeRelationshipUncheckedCreateNestedManyWithoutFromInputSchema).optional(),
  relationshipsTo: z.lazy(() => NodeRelationshipUncheckedCreateNestedManyWithoutToInputSchema).optional(),
  topics: z.lazy(() => TopicUncheckedCreateNestedManyWithoutNodesInputSchema).optional()
}).strict();

export const KnowledgeNodeCreateOrConnectWithoutValidationsInputSchema: z.ZodType<Prisma.KnowledgeNodeCreateOrConnectWithoutValidationsInput> = z.object({
  where: z.lazy(() => KnowledgeNodeWhereUniqueInputSchema),
  create: z.union([ z.lazy(() => KnowledgeNodeCreateWithoutValidationsInputSchema),z.lazy(() => KnowledgeNodeUncheckedCreateWithoutValidationsInputSchema) ]),
}).strict();

export const UserCreateWithoutValidationsInputSchema: z.ZodType<Prisma.UserCreateWithoutValidationsInput> = z.object({
  userId: z.string(),
  nodes: z.lazy(() => KnowledgeNodeCreateNestedManyWithoutAuthorInputSchema).optional()
}).strict();

export const UserUncheckedCreateWithoutValidationsInputSchema: z.ZodType<Prisma.UserUncheckedCreateWithoutValidationsInput> = z.object({
  userId: z.string(),
  nodes: z.lazy(() => KnowledgeNodeUncheckedCreateNestedManyWithoutAuthorInputSchema).optional()
}).strict();

export const UserCreateOrConnectWithoutValidationsInputSchema: z.ZodType<Prisma.UserCreateOrConnectWithoutValidationsInput> = z.object({
  where: z.lazy(() => UserWhereUniqueInputSchema),
  create: z.union([ z.lazy(() => UserCreateWithoutValidationsInputSchema),z.lazy(() => UserUncheckedCreateWithoutValidationsInputSchema) ]),
}).strict();

export const KnowledgeNodeUpsertWithoutValidationsInputSchema: z.ZodType<Prisma.KnowledgeNodeUpsertWithoutValidationsInput> = z.object({
  update: z.union([ z.lazy(() => KnowledgeNodeUpdateWithoutValidationsInputSchema),z.lazy(() => KnowledgeNodeUncheckedUpdateWithoutValidationsInputSchema) ]),
  create: z.union([ z.lazy(() => KnowledgeNodeCreateWithoutValidationsInputSchema),z.lazy(() => KnowledgeNodeUncheckedCreateWithoutValidationsInputSchema) ]),
  where: z.lazy(() => KnowledgeNodeWhereInputSchema).optional()
}).strict();

export const KnowledgeNodeUpdateToOneWithWhereWithoutValidationsInputSchema: z.ZodType<Prisma.KnowledgeNodeUpdateToOneWithWhereWithoutValidationsInput> = z.object({
  where: z.lazy(() => KnowledgeNodeWhereInputSchema).optional(),
  data: z.union([ z.lazy(() => KnowledgeNodeUpdateWithoutValidationsInputSchema),z.lazy(() => KnowledgeNodeUncheckedUpdateWithoutValidationsInputSchema) ]),
}).strict();

export const KnowledgeNodeUpdateWithoutValidationsInputSchema: z.ZodType<Prisma.KnowledgeNodeUpdateWithoutValidationsInput> = z.object({
  id: z.union([ z.string().cuid(),z.lazy(() => StringFieldUpdateOperationsInputSchema) ]).optional(),
  content: z.union([ z.string(),z.lazy(() => StringFieldUpdateOperationsInputSchema) ]).optional(),
  embedding: z.union([ z.lazy(() => KnowledgeNodeUpdateembeddingInputSchema),z.number().array() ]).optional(),
  type: z.union([ z.lazy(() => NodeTypeSchema),z.lazy(() => EnumNodeTypeFieldUpdateOperationsInputSchema) ]).optional(),
  source: z.union([ z.lazy(() => SourceTypeSchema),z.lazy(() => EnumSourceTypeFieldUpdateOperationsInputSchema) ]).optional(),
  context: z.union([ z.lazy(() => NullableJsonNullValueInputSchema),InputJsonValueSchema ]).optional(),
  validated: z.union([ z.boolean(),z.lazy(() => BoolFieldUpdateOperationsInputSchema) ]).optional(),
  createdAt: z.union([ z.coerce.date(),z.lazy(() => DateTimeFieldUpdateOperationsInputSchema) ]).optional(),
  updatedAt: z.union([ z.coerce.date(),z.lazy(() => DateTimeFieldUpdateOperationsInputSchema) ]).optional(),
  author: z.lazy(() => UserUpdateOneRequiredWithoutNodesNestedInputSchema).optional(),
  relationshipsFrom: z.lazy(() => NodeRelationshipUpdateManyWithoutFromNestedInputSchema).optional(),
  relationshipsTo: z.lazy(() => NodeRelationshipUpdateManyWithoutToNestedInputSchema).optional(),
  topics: z.lazy(() => TopicUpdateManyWithoutNodesNestedInputSchema).optional()
}).strict();

export const KnowledgeNodeUncheckedUpdateWithoutValidationsInputSchema: z.ZodType<Prisma.KnowledgeNodeUncheckedUpdateWithoutValidationsInput> = z.object({
  id: z.union([ z.string().cuid(),z.lazy(() => StringFieldUpdateOperationsInputSchema) ]).optional(),
  content: z.union([ z.string(),z.lazy(() => StringFieldUpdateOperationsInputSchema) ]).optional(),
  embedding: z.union([ z.lazy(() => KnowledgeNodeUpdateembeddingInputSchema),z.number().array() ]).optional(),
  type: z.union([ z.lazy(() => NodeTypeSchema),z.lazy(() => EnumNodeTypeFieldUpdateOperationsInputSchema) ]).optional(),
  source: z.union([ z.lazy(() => SourceTypeSchema),z.lazy(() => EnumSourceTypeFieldUpdateOperationsInputSchema) ]).optional(),
  context: z.union([ z.lazy(() => NullableJsonNullValueInputSchema),InputJsonValueSchema ]).optional(),
  validated: z.union([ z.boolean(),z.lazy(() => BoolFieldUpdateOperationsInputSchema) ]).optional(),
  createdAt: z.union([ z.coerce.date(),z.lazy(() => DateTimeFieldUpdateOperationsInputSchema) ]).optional(),
  updatedAt: z.union([ z.coerce.date(),z.lazy(() => DateTimeFieldUpdateOperationsInputSchema) ]).optional(),
  authorId: z.union([ z.string(),z.lazy(() => StringFieldUpdateOperationsInputSchema) ]).optional(),
  relationshipsFrom: z.lazy(() => NodeRelationshipUncheckedUpdateManyWithoutFromNestedInputSchema).optional(),
  relationshipsTo: z.lazy(() => NodeRelationshipUncheckedUpdateManyWithoutToNestedInputSchema).optional(),
  topics: z.lazy(() => TopicUncheckedUpdateManyWithoutNodesNestedInputSchema).optional()
}).strict();

export const UserUpsertWithoutValidationsInputSchema: z.ZodType<Prisma.UserUpsertWithoutValidationsInput> = z.object({
  update: z.union([ z.lazy(() => UserUpdateWithoutValidationsInputSchema),z.lazy(() => UserUncheckedUpdateWithoutValidationsInputSchema) ]),
  create: z.union([ z.lazy(() => UserCreateWithoutValidationsInputSchema),z.lazy(() => UserUncheckedCreateWithoutValidationsInputSchema) ]),
  where: z.lazy(() => UserWhereInputSchema).optional()
}).strict();

export const UserUpdateToOneWithWhereWithoutValidationsInputSchema: z.ZodType<Prisma.UserUpdateToOneWithWhereWithoutValidationsInput> = z.object({
  where: z.lazy(() => UserWhereInputSchema).optional(),
  data: z.union([ z.lazy(() => UserUpdateWithoutValidationsInputSchema),z.lazy(() => UserUncheckedUpdateWithoutValidationsInputSchema) ]),
}).strict();

export const UserUpdateWithoutValidationsInputSchema: z.ZodType<Prisma.UserUpdateWithoutValidationsInput> = z.object({
  userId: z.union([ z.string(),z.lazy(() => StringFieldUpdateOperationsInputSchema) ]).optional(),
  nodes: z.lazy(() => KnowledgeNodeUpdateManyWithoutAuthorNestedInputSchema).optional()
}).strict();

export const UserUncheckedUpdateWithoutValidationsInputSchema: z.ZodType<Prisma.UserUncheckedUpdateWithoutValidationsInput> = z.object({
  userId: z.union([ z.string(),z.lazy(() => StringFieldUpdateOperationsInputSchema) ]).optional(),
  nodes: z.lazy(() => KnowledgeNodeUncheckedUpdateManyWithoutAuthorNestedInputSchema).optional()
}).strict();

export const KnowledgeNodeCreateWithoutTopicsInputSchema: z.ZodType<Prisma.KnowledgeNodeCreateWithoutTopicsInput> = z.object({
  id: z.string().cuid().optional(),
  content: z.string(),
  embedding: z.union([ z.lazy(() => KnowledgeNodeCreateembeddingInputSchema),z.number().array() ]).optional(),
  type: z.lazy(() => NodeTypeSchema),
  source: z.lazy(() => SourceTypeSchema),
  context: z.union([ z.lazy(() => NullableJsonNullValueInputSchema),InputJsonValueSchema ]).optional(),
  validated: z.boolean().optional(),
  createdAt: z.coerce.date().optional(),
  updatedAt: z.coerce.date().optional(),
  author: z.lazy(() => UserCreateNestedOneWithoutNodesInputSchema),
  relationshipsFrom: z.lazy(() => NodeRelationshipCreateNestedManyWithoutFromInputSchema).optional(),
  relationshipsTo: z.lazy(() => NodeRelationshipCreateNestedManyWithoutToInputSchema).optional(),
  validations: z.lazy(() => ValidationCreateNestedManyWithoutNodeInputSchema).optional()
}).strict();

export const KnowledgeNodeUncheckedCreateWithoutTopicsInputSchema: z.ZodType<Prisma.KnowledgeNodeUncheckedCreateWithoutTopicsInput> = z.object({
  id: z.string().cuid().optional(),
  content: z.string(),
  embedding: z.union([ z.lazy(() => KnowledgeNodeCreateembeddingInputSchema),z.number().array() ]).optional(),
  type: z.lazy(() => NodeTypeSchema),
  source: z.lazy(() => SourceTypeSchema),
  context: z.union([ z.lazy(() => NullableJsonNullValueInputSchema),InputJsonValueSchema ]).optional(),
  validated: z.boolean().optional(),
  createdAt: z.coerce.date().optional(),
  updatedAt: z.coerce.date().optional(),
  authorId: z.string(),
  relationshipsFrom: z.lazy(() => NodeRelationshipUncheckedCreateNestedManyWithoutFromInputSchema).optional(),
  relationshipsTo: z.lazy(() => NodeRelationshipUncheckedCreateNestedManyWithoutToInputSchema).optional(),
  validations: z.lazy(() => ValidationUncheckedCreateNestedManyWithoutNodeInputSchema).optional()
}).strict();

export const KnowledgeNodeCreateOrConnectWithoutTopicsInputSchema: z.ZodType<Prisma.KnowledgeNodeCreateOrConnectWithoutTopicsInput> = z.object({
  where: z.lazy(() => KnowledgeNodeWhereUniqueInputSchema),
  create: z.union([ z.lazy(() => KnowledgeNodeCreateWithoutTopicsInputSchema),z.lazy(() => KnowledgeNodeUncheckedCreateWithoutTopicsInputSchema) ]),
}).strict();

export const KnowledgeNodeUpsertWithWhereUniqueWithoutTopicsInputSchema: z.ZodType<Prisma.KnowledgeNodeUpsertWithWhereUniqueWithoutTopicsInput> = z.object({
  where: z.lazy(() => KnowledgeNodeWhereUniqueInputSchema),
  update: z.union([ z.lazy(() => KnowledgeNodeUpdateWithoutTopicsInputSchema),z.lazy(() => KnowledgeNodeUncheckedUpdateWithoutTopicsInputSchema) ]),
  create: z.union([ z.lazy(() => KnowledgeNodeCreateWithoutTopicsInputSchema),z.lazy(() => KnowledgeNodeUncheckedCreateWithoutTopicsInputSchema) ]),
}).strict();

export const KnowledgeNodeUpdateWithWhereUniqueWithoutTopicsInputSchema: z.ZodType<Prisma.KnowledgeNodeUpdateWithWhereUniqueWithoutTopicsInput> = z.object({
  where: z.lazy(() => KnowledgeNodeWhereUniqueInputSchema),
  data: z.union([ z.lazy(() => KnowledgeNodeUpdateWithoutTopicsInputSchema),z.lazy(() => KnowledgeNodeUncheckedUpdateWithoutTopicsInputSchema) ]),
}).strict();

export const KnowledgeNodeUpdateManyWithWhereWithoutTopicsInputSchema: z.ZodType<Prisma.KnowledgeNodeUpdateManyWithWhereWithoutTopicsInput> = z.object({
  where: z.lazy(() => KnowledgeNodeScalarWhereInputSchema),
  data: z.union([ z.lazy(() => KnowledgeNodeUpdateManyMutationInputSchema),z.lazy(() => KnowledgeNodeUncheckedUpdateManyWithoutTopicsInputSchema) ]),
}).strict();

export const KnowledgeNodeCreateManyAuthorInputSchema: z.ZodType<Prisma.KnowledgeNodeCreateManyAuthorInput> = z.object({
  id: z.string().cuid().optional(),
  content: z.string(),
  embedding: z.union([ z.lazy(() => KnowledgeNodeCreateembeddingInputSchema),z.number().array() ]).optional(),
  type: z.lazy(() => NodeTypeSchema),
  source: z.lazy(() => SourceTypeSchema),
  context: z.union([ z.lazy(() => NullableJsonNullValueInputSchema),InputJsonValueSchema ]).optional(),
  validated: z.boolean().optional(),
  createdAt: z.coerce.date().optional(),
  updatedAt: z.coerce.date().optional()
}).strict();

export const ValidationCreateManyExpertInputSchema: z.ZodType<Prisma.ValidationCreateManyExpertInput> = z.object({
  id: z.string().cuid().optional(),
  nodeId: z.string(),
  notes: z.string().optional().nullable(),
  createdAt: z.coerce.date().optional(),
  updatedAt: z.coerce.date().optional()
}).strict();

export const KnowledgeNodeUpdateWithoutAuthorInputSchema: z.ZodType<Prisma.KnowledgeNodeUpdateWithoutAuthorInput> = z.object({
  id: z.union([ z.string().cuid(),z.lazy(() => StringFieldUpdateOperationsInputSchema) ]).optional(),
  content: z.union([ z.string(),z.lazy(() => StringFieldUpdateOperationsInputSchema) ]).optional(),
  embedding: z.union([ z.lazy(() => KnowledgeNodeUpdateembeddingInputSchema),z.number().array() ]).optional(),
  type: z.union([ z.lazy(() => NodeTypeSchema),z.lazy(() => EnumNodeTypeFieldUpdateOperationsInputSchema) ]).optional(),
  source: z.union([ z.lazy(() => SourceTypeSchema),z.lazy(() => EnumSourceTypeFieldUpdateOperationsInputSchema) ]).optional(),
  context: z.union([ z.lazy(() => NullableJsonNullValueInputSchema),InputJsonValueSchema ]).optional(),
  validated: z.union([ z.boolean(),z.lazy(() => BoolFieldUpdateOperationsInputSchema) ]).optional(),
  createdAt: z.union([ z.coerce.date(),z.lazy(() => DateTimeFieldUpdateOperationsInputSchema) ]).optional(),
  updatedAt: z.union([ z.coerce.date(),z.lazy(() => DateTimeFieldUpdateOperationsInputSchema) ]).optional(),
  relationshipsFrom: z.lazy(() => NodeRelationshipUpdateManyWithoutFromNestedInputSchema).optional(),
  relationshipsTo: z.lazy(() => NodeRelationshipUpdateManyWithoutToNestedInputSchema).optional(),
  validations: z.lazy(() => ValidationUpdateManyWithoutNodeNestedInputSchema).optional(),
  topics: z.lazy(() => TopicUpdateManyWithoutNodesNestedInputSchema).optional()
}).strict();

export const KnowledgeNodeUncheckedUpdateWithoutAuthorInputSchema: z.ZodType<Prisma.KnowledgeNodeUncheckedUpdateWithoutAuthorInput> = z.object({
  id: z.union([ z.string().cuid(),z.lazy(() => StringFieldUpdateOperationsInputSchema) ]).optional(),
  content: z.union([ z.string(),z.lazy(() => StringFieldUpdateOperationsInputSchema) ]).optional(),
  embedding: z.union([ z.lazy(() => KnowledgeNodeUpdateembeddingInputSchema),z.number().array() ]).optional(),
  type: z.union([ z.lazy(() => NodeTypeSchema),z.lazy(() => EnumNodeTypeFieldUpdateOperationsInputSchema) ]).optional(),
  source: z.union([ z.lazy(() => SourceTypeSchema),z.lazy(() => EnumSourceTypeFieldUpdateOperationsInputSchema) ]).optional(),
  context: z.union([ z.lazy(() => NullableJsonNullValueInputSchema),InputJsonValueSchema ]).optional(),
  validated: z.union([ z.boolean(),z.lazy(() => BoolFieldUpdateOperationsInputSchema) ]).optional(),
  createdAt: z.union([ z.coerce.date(),z.lazy(() => DateTimeFieldUpdateOperationsInputSchema) ]).optional(),
  updatedAt: z.union([ z.coerce.date(),z.lazy(() => DateTimeFieldUpdateOperationsInputSchema) ]).optional(),
  relationshipsFrom: z.lazy(() => NodeRelationshipUncheckedUpdateManyWithoutFromNestedInputSchema).optional(),
  relationshipsTo: z.lazy(() => NodeRelationshipUncheckedUpdateManyWithoutToNestedInputSchema).optional(),
  validations: z.lazy(() => ValidationUncheckedUpdateManyWithoutNodeNestedInputSchema).optional(),
  topics: z.lazy(() => TopicUncheckedUpdateManyWithoutNodesNestedInputSchema).optional()
}).strict();

export const KnowledgeNodeUncheckedUpdateManyWithoutAuthorInputSchema: z.ZodType<Prisma.KnowledgeNodeUncheckedUpdateManyWithoutAuthorInput> = z.object({
  id: z.union([ z.string().cuid(),z.lazy(() => StringFieldUpdateOperationsInputSchema) ]).optional(),
  content: z.union([ z.string(),z.lazy(() => StringFieldUpdateOperationsInputSchema) ]).optional(),
  embedding: z.union([ z.lazy(() => KnowledgeNodeUpdateembeddingInputSchema),z.number().array() ]).optional(),
  type: z.union([ z.lazy(() => NodeTypeSchema),z.lazy(() => EnumNodeTypeFieldUpdateOperationsInputSchema) ]).optional(),
  source: z.union([ z.lazy(() => SourceTypeSchema),z.lazy(() => EnumSourceTypeFieldUpdateOperationsInputSchema) ]).optional(),
  context: z.union([ z.lazy(() => NullableJsonNullValueInputSchema),InputJsonValueSchema ]).optional(),
  validated: z.union([ z.boolean(),z.lazy(() => BoolFieldUpdateOperationsInputSchema) ]).optional(),
  createdAt: z.union([ z.coerce.date(),z.lazy(() => DateTimeFieldUpdateOperationsInputSchema) ]).optional(),
  updatedAt: z.union([ z.coerce.date(),z.lazy(() => DateTimeFieldUpdateOperationsInputSchema) ]).optional(),
}).strict();

export const ValidationUpdateWithoutExpertInputSchema: z.ZodType<Prisma.ValidationUpdateWithoutExpertInput> = z.object({
  id: z.union([ z.string().cuid(),z.lazy(() => StringFieldUpdateOperationsInputSchema) ]).optional(),
  notes: z.union([ z.string(),z.lazy(() => NullableStringFieldUpdateOperationsInputSchema) ]).optional().nullable(),
  createdAt: z.union([ z.coerce.date(),z.lazy(() => DateTimeFieldUpdateOperationsInputSchema) ]).optional(),
  updatedAt: z.union([ z.coerce.date(),z.lazy(() => DateTimeFieldUpdateOperationsInputSchema) ]).optional(),
  node: z.lazy(() => KnowledgeNodeUpdateOneRequiredWithoutValidationsNestedInputSchema).optional()
}).strict();

export const ValidationUncheckedUpdateWithoutExpertInputSchema: z.ZodType<Prisma.ValidationUncheckedUpdateWithoutExpertInput> = z.object({
  id: z.union([ z.string().cuid(),z.lazy(() => StringFieldUpdateOperationsInputSchema) ]).optional(),
  nodeId: z.union([ z.string(),z.lazy(() => StringFieldUpdateOperationsInputSchema) ]).optional(),
  notes: z.union([ z.string(),z.lazy(() => NullableStringFieldUpdateOperationsInputSchema) ]).optional().nullable(),
  createdAt: z.union([ z.coerce.date(),z.lazy(() => DateTimeFieldUpdateOperationsInputSchema) ]).optional(),
  updatedAt: z.union([ z.coerce.date(),z.lazy(() => DateTimeFieldUpdateOperationsInputSchema) ]).optional(),
}).strict();

export const ValidationUncheckedUpdateManyWithoutExpertInputSchema: z.ZodType<Prisma.ValidationUncheckedUpdateManyWithoutExpertInput> = z.object({
  id: z.union([ z.string().cuid(),z.lazy(() => StringFieldUpdateOperationsInputSchema) ]).optional(),
  nodeId: z.union([ z.string(),z.lazy(() => StringFieldUpdateOperationsInputSchema) ]).optional(),
  notes: z.union([ z.string(),z.lazy(() => NullableStringFieldUpdateOperationsInputSchema) ]).optional().nullable(),
  createdAt: z.union([ z.coerce.date(),z.lazy(() => DateTimeFieldUpdateOperationsInputSchema) ]).optional(),
  updatedAt: z.union([ z.coerce.date(),z.lazy(() => DateTimeFieldUpdateOperationsInputSchema) ]).optional(),
}).strict();

export const NodeRelationshipCreateManyFromInputSchema: z.ZodType<Prisma.NodeRelationshipCreateManyFromInput> = z.object({
  id: z.string().cuid().optional(),
  toId: z.string(),
  type: z.lazy(() => RelationshipTypeSchema),
  createdAt: z.coerce.date().optional(),
  updatedAt: z.coerce.date().optional()
}).strict();

export const NodeRelationshipCreateManyToInputSchema: z.ZodType<Prisma.NodeRelationshipCreateManyToInput> = z.object({
  id: z.string().cuid().optional(),
  fromId: z.string(),
  type: z.lazy(() => RelationshipTypeSchema),
  createdAt: z.coerce.date().optional(),
  updatedAt: z.coerce.date().optional()
}).strict();

export const ValidationCreateManyNodeInputSchema: z.ZodType<Prisma.ValidationCreateManyNodeInput> = z.object({
  id: z.string().cuid().optional(),
  expertId: z.string(),
  notes: z.string().optional().nullable(),
  createdAt: z.coerce.date().optional(),
  updatedAt: z.coerce.date().optional()
}).strict();

export const NodeRelationshipUpdateWithoutFromInputSchema: z.ZodType<Prisma.NodeRelationshipUpdateWithoutFromInput> = z.object({
  id: z.union([ z.string().cuid(),z.lazy(() => StringFieldUpdateOperationsInputSchema) ]).optional(),
  type: z.union([ z.lazy(() => RelationshipTypeSchema),z.lazy(() => EnumRelationshipTypeFieldUpdateOperationsInputSchema) ]).optional(),
  createdAt: z.union([ z.coerce.date(),z.lazy(() => DateTimeFieldUpdateOperationsInputSchema) ]).optional(),
  updatedAt: z.union([ z.coerce.date(),z.lazy(() => DateTimeFieldUpdateOperationsInputSchema) ]).optional(),
  to: z.lazy(() => KnowledgeNodeUpdateOneRequiredWithoutRelationshipsToNestedInputSchema).optional()
}).strict();

export const NodeRelationshipUncheckedUpdateWithoutFromInputSchema: z.ZodType<Prisma.NodeRelationshipUncheckedUpdateWithoutFromInput> = z.object({
  id: z.union([ z.string().cuid(),z.lazy(() => StringFieldUpdateOperationsInputSchema) ]).optional(),
  toId: z.union([ z.string(),z.lazy(() => StringFieldUpdateOperationsInputSchema) ]).optional(),
  type: z.union([ z.lazy(() => RelationshipTypeSchema),z.lazy(() => EnumRelationshipTypeFieldUpdateOperationsInputSchema) ]).optional(),
  createdAt: z.union([ z.coerce.date(),z.lazy(() => DateTimeFieldUpdateOperationsInputSchema) ]).optional(),
  updatedAt: z.union([ z.coerce.date(),z.lazy(() => DateTimeFieldUpdateOperationsInputSchema) ]).optional(),
}).strict();

export const NodeRelationshipUncheckedUpdateManyWithoutFromInputSchema: z.ZodType<Prisma.NodeRelationshipUncheckedUpdateManyWithoutFromInput> = z.object({
  id: z.union([ z.string().cuid(),z.lazy(() => StringFieldUpdateOperationsInputSchema) ]).optional(),
  toId: z.union([ z.string(),z.lazy(() => StringFieldUpdateOperationsInputSchema) ]).optional(),
  type: z.union([ z.lazy(() => RelationshipTypeSchema),z.lazy(() => EnumRelationshipTypeFieldUpdateOperationsInputSchema) ]).optional(),
  createdAt: z.union([ z.coerce.date(),z.lazy(() => DateTimeFieldUpdateOperationsInputSchema) ]).optional(),
  updatedAt: z.union([ z.coerce.date(),z.lazy(() => DateTimeFieldUpdateOperationsInputSchema) ]).optional(),
}).strict();

export const NodeRelationshipUpdateWithoutToInputSchema: z.ZodType<Prisma.NodeRelationshipUpdateWithoutToInput> = z.object({
  id: z.union([ z.string().cuid(),z.lazy(() => StringFieldUpdateOperationsInputSchema) ]).optional(),
  type: z.union([ z.lazy(() => RelationshipTypeSchema),z.lazy(() => EnumRelationshipTypeFieldUpdateOperationsInputSchema) ]).optional(),
  createdAt: z.union([ z.coerce.date(),z.lazy(() => DateTimeFieldUpdateOperationsInputSchema) ]).optional(),
  updatedAt: z.union([ z.coerce.date(),z.lazy(() => DateTimeFieldUpdateOperationsInputSchema) ]).optional(),
  from: z.lazy(() => KnowledgeNodeUpdateOneRequiredWithoutRelationshipsFromNestedInputSchema).optional()
}).strict();

export const NodeRelationshipUncheckedUpdateWithoutToInputSchema: z.ZodType<Prisma.NodeRelationshipUncheckedUpdateWithoutToInput> = z.object({
  id: z.union([ z.string().cuid(),z.lazy(() => StringFieldUpdateOperationsInputSchema) ]).optional(),
  fromId: z.union([ z.string(),z.lazy(() => StringFieldUpdateOperationsInputSchema) ]).optional(),
  type: z.union([ z.lazy(() => RelationshipTypeSchema),z.lazy(() => EnumRelationshipTypeFieldUpdateOperationsInputSchema) ]).optional(),
  createdAt: z.union([ z.coerce.date(),z.lazy(() => DateTimeFieldUpdateOperationsInputSchema) ]).optional(),
  updatedAt: z.union([ z.coerce.date(),z.lazy(() => DateTimeFieldUpdateOperationsInputSchema) ]).optional(),
}).strict();

export const NodeRelationshipUncheckedUpdateManyWithoutToInputSchema: z.ZodType<Prisma.NodeRelationshipUncheckedUpdateManyWithoutToInput> = z.object({
  id: z.union([ z.string().cuid(),z.lazy(() => StringFieldUpdateOperationsInputSchema) ]).optional(),
  fromId: z.union([ z.string(),z.lazy(() => StringFieldUpdateOperationsInputSchema) ]).optional(),
  type: z.union([ z.lazy(() => RelationshipTypeSchema),z.lazy(() => EnumRelationshipTypeFieldUpdateOperationsInputSchema) ]).optional(),
  createdAt: z.union([ z.coerce.date(),z.lazy(() => DateTimeFieldUpdateOperationsInputSchema) ]).optional(),
  updatedAt: z.union([ z.coerce.date(),z.lazy(() => DateTimeFieldUpdateOperationsInputSchema) ]).optional(),
}).strict();

export const ValidationUpdateWithoutNodeInputSchema: z.ZodType<Prisma.ValidationUpdateWithoutNodeInput> = z.object({
  id: z.union([ z.string().cuid(),z.lazy(() => StringFieldUpdateOperationsInputSchema) ]).optional(),
  notes: z.union([ z.string(),z.lazy(() => NullableStringFieldUpdateOperationsInputSchema) ]).optional().nullable(),
  createdAt: z.union([ z.coerce.date(),z.lazy(() => DateTimeFieldUpdateOperationsInputSchema) ]).optional(),
  updatedAt: z.union([ z.coerce.date(),z.lazy(() => DateTimeFieldUpdateOperationsInputSchema) ]).optional(),
  expert: z.lazy(() => UserUpdateOneRequiredWithoutValidationsNestedInputSchema).optional()
}).strict();

export const ValidationUncheckedUpdateWithoutNodeInputSchema: z.ZodType<Prisma.ValidationUncheckedUpdateWithoutNodeInput> = z.object({
  id: z.union([ z.string().cuid(),z.lazy(() => StringFieldUpdateOperationsInputSchema) ]).optional(),
  expertId: z.union([ z.string(),z.lazy(() => StringFieldUpdateOperationsInputSchema) ]).optional(),
  notes: z.union([ z.string(),z.lazy(() => NullableStringFieldUpdateOperationsInputSchema) ]).optional().nullable(),
  createdAt: z.union([ z.coerce.date(),z.lazy(() => DateTimeFieldUpdateOperationsInputSchema) ]).optional(),
  updatedAt: z.union([ z.coerce.date(),z.lazy(() => DateTimeFieldUpdateOperationsInputSchema) ]).optional(),
}).strict();

export const ValidationUncheckedUpdateManyWithoutNodeInputSchema: z.ZodType<Prisma.ValidationUncheckedUpdateManyWithoutNodeInput> = z.object({
  id: z.union([ z.string().cuid(),z.lazy(() => StringFieldUpdateOperationsInputSchema) ]).optional(),
  expertId: z.union([ z.string(),z.lazy(() => StringFieldUpdateOperationsInputSchema) ]).optional(),
  notes: z.union([ z.string(),z.lazy(() => NullableStringFieldUpdateOperationsInputSchema) ]).optional().nullable(),
  createdAt: z.union([ z.coerce.date(),z.lazy(() => DateTimeFieldUpdateOperationsInputSchema) ]).optional(),
  updatedAt: z.union([ z.coerce.date(),z.lazy(() => DateTimeFieldUpdateOperationsInputSchema) ]).optional(),
}).strict();

export const TopicUpdateWithoutNodesInputSchema: z.ZodType<Prisma.TopicUpdateWithoutNodesInput> = z.object({
  id: z.union([ z.string().cuid(),z.lazy(() => StringFieldUpdateOperationsInputSchema) ]).optional(),
  name: z.union([ z.string(),z.lazy(() => StringFieldUpdateOperationsInputSchema) ]).optional(),
  createdAt: z.union([ z.coerce.date(),z.lazy(() => DateTimeFieldUpdateOperationsInputSchema) ]).optional(),
  updatedAt: z.union([ z.coerce.date(),z.lazy(() => DateTimeFieldUpdateOperationsInputSchema) ]).optional(),
}).strict();

export const TopicUncheckedUpdateWithoutNodesInputSchema: z.ZodType<Prisma.TopicUncheckedUpdateWithoutNodesInput> = z.object({
  id: z.union([ z.string().cuid(),z.lazy(() => StringFieldUpdateOperationsInputSchema) ]).optional(),
  name: z.union([ z.string(),z.lazy(() => StringFieldUpdateOperationsInputSchema) ]).optional(),
  createdAt: z.union([ z.coerce.date(),z.lazy(() => DateTimeFieldUpdateOperationsInputSchema) ]).optional(),
  updatedAt: z.union([ z.coerce.date(),z.lazy(() => DateTimeFieldUpdateOperationsInputSchema) ]).optional(),
}).strict();

export const TopicUncheckedUpdateManyWithoutNodesInputSchema: z.ZodType<Prisma.TopicUncheckedUpdateManyWithoutNodesInput> = z.object({
  id: z.union([ z.string().cuid(),z.lazy(() => StringFieldUpdateOperationsInputSchema) ]).optional(),
  name: z.union([ z.string(),z.lazy(() => StringFieldUpdateOperationsInputSchema) ]).optional(),
  createdAt: z.union([ z.coerce.date(),z.lazy(() => DateTimeFieldUpdateOperationsInputSchema) ]).optional(),
  updatedAt: z.union([ z.coerce.date(),z.lazy(() => DateTimeFieldUpdateOperationsInputSchema) ]).optional(),
}).strict();

export const KnowledgeNodeUpdateWithoutTopicsInputSchema: z.ZodType<Prisma.KnowledgeNodeUpdateWithoutTopicsInput> = z.object({
  id: z.union([ z.string().cuid(),z.lazy(() => StringFieldUpdateOperationsInputSchema) ]).optional(),
  content: z.union([ z.string(),z.lazy(() => StringFieldUpdateOperationsInputSchema) ]).optional(),
  embedding: z.union([ z.lazy(() => KnowledgeNodeUpdateembeddingInputSchema),z.number().array() ]).optional(),
  type: z.union([ z.lazy(() => NodeTypeSchema),z.lazy(() => EnumNodeTypeFieldUpdateOperationsInputSchema) ]).optional(),
  source: z.union([ z.lazy(() => SourceTypeSchema),z.lazy(() => EnumSourceTypeFieldUpdateOperationsInputSchema) ]).optional(),
  context: z.union([ z.lazy(() => NullableJsonNullValueInputSchema),InputJsonValueSchema ]).optional(),
  validated: z.union([ z.boolean(),z.lazy(() => BoolFieldUpdateOperationsInputSchema) ]).optional(),
  createdAt: z.union([ z.coerce.date(),z.lazy(() => DateTimeFieldUpdateOperationsInputSchema) ]).optional(),
  updatedAt: z.union([ z.coerce.date(),z.lazy(() => DateTimeFieldUpdateOperationsInputSchema) ]).optional(),
  author: z.lazy(() => UserUpdateOneRequiredWithoutNodesNestedInputSchema).optional(),
  relationshipsFrom: z.lazy(() => NodeRelationshipUpdateManyWithoutFromNestedInputSchema).optional(),
  relationshipsTo: z.lazy(() => NodeRelationshipUpdateManyWithoutToNestedInputSchema).optional(),
  validations: z.lazy(() => ValidationUpdateManyWithoutNodeNestedInputSchema).optional()
}).strict();

export const KnowledgeNodeUncheckedUpdateWithoutTopicsInputSchema: z.ZodType<Prisma.KnowledgeNodeUncheckedUpdateWithoutTopicsInput> = z.object({
  id: z.union([ z.string().cuid(),z.lazy(() => StringFieldUpdateOperationsInputSchema) ]).optional(),
  content: z.union([ z.string(),z.lazy(() => StringFieldUpdateOperationsInputSchema) ]).optional(),
  embedding: z.union([ z.lazy(() => KnowledgeNodeUpdateembeddingInputSchema),z.number().array() ]).optional(),
  type: z.union([ z.lazy(() => NodeTypeSchema),z.lazy(() => EnumNodeTypeFieldUpdateOperationsInputSchema) ]).optional(),
  source: z.union([ z.lazy(() => SourceTypeSchema),z.lazy(() => EnumSourceTypeFieldUpdateOperationsInputSchema) ]).optional(),
  context: z.union([ z.lazy(() => NullableJsonNullValueInputSchema),InputJsonValueSchema ]).optional(),
  validated: z.union([ z.boolean(),z.lazy(() => BoolFieldUpdateOperationsInputSchema) ]).optional(),
  createdAt: z.union([ z.coerce.date(),z.lazy(() => DateTimeFieldUpdateOperationsInputSchema) ]).optional(),
  updatedAt: z.union([ z.coerce.date(),z.lazy(() => DateTimeFieldUpdateOperationsInputSchema) ]).optional(),
  authorId: z.union([ z.string(),z.lazy(() => StringFieldUpdateOperationsInputSchema) ]).optional(),
  relationshipsFrom: z.lazy(() => NodeRelationshipUncheckedUpdateManyWithoutFromNestedInputSchema).optional(),
  relationshipsTo: z.lazy(() => NodeRelationshipUncheckedUpdateManyWithoutToNestedInputSchema).optional(),
  validations: z.lazy(() => ValidationUncheckedUpdateManyWithoutNodeNestedInputSchema).optional()
}).strict();

export const KnowledgeNodeUncheckedUpdateManyWithoutTopicsInputSchema: z.ZodType<Prisma.KnowledgeNodeUncheckedUpdateManyWithoutTopicsInput> = z.object({
  id: z.union([ z.string().cuid(),z.lazy(() => StringFieldUpdateOperationsInputSchema) ]).optional(),
  content: z.union([ z.string(),z.lazy(() => StringFieldUpdateOperationsInputSchema) ]).optional(),
  embedding: z.union([ z.lazy(() => KnowledgeNodeUpdateembeddingInputSchema),z.number().array() ]).optional(),
  type: z.union([ z.lazy(() => NodeTypeSchema),z.lazy(() => EnumNodeTypeFieldUpdateOperationsInputSchema) ]).optional(),
  source: z.union([ z.lazy(() => SourceTypeSchema),z.lazy(() => EnumSourceTypeFieldUpdateOperationsInputSchema) ]).optional(),
  context: z.union([ z.lazy(() => NullableJsonNullValueInputSchema),InputJsonValueSchema ]).optional(),
  validated: z.union([ z.boolean(),z.lazy(() => BoolFieldUpdateOperationsInputSchema) ]).optional(),
  createdAt: z.union([ z.coerce.date(),z.lazy(() => DateTimeFieldUpdateOperationsInputSchema) ]).optional(),
  updatedAt: z.union([ z.coerce.date(),z.lazy(() => DateTimeFieldUpdateOperationsInputSchema) ]).optional(),
  authorId: z.union([ z.string(),z.lazy(() => StringFieldUpdateOperationsInputSchema) ]).optional(),
}).strict();

/////////////////////////////////////////
// ARGS
/////////////////////////////////////////

export const FooFindFirstArgsSchema: z.ZodType<Prisma.FooFindFirstArgs> = z.object({
  select: FooSelectSchema.optional(),
  where: FooWhereInputSchema.optional(),
  orderBy: z.union([ FooOrderByWithRelationInputSchema.array(),FooOrderByWithRelationInputSchema ]).optional(),
  cursor: FooWhereUniqueInputSchema.optional(),
  take: z.number().optional(),
  skip: z.number().optional(),
  distinct: z.union([ FooScalarFieldEnumSchema,FooScalarFieldEnumSchema.array() ]).optional(),
}).strict() ;

export const FooFindFirstOrThrowArgsSchema: z.ZodType<Prisma.FooFindFirstOrThrowArgs> = z.object({
  select: FooSelectSchema.optional(),
  where: FooWhereInputSchema.optional(),
  orderBy: z.union([ FooOrderByWithRelationInputSchema.array(),FooOrderByWithRelationInputSchema ]).optional(),
  cursor: FooWhereUniqueInputSchema.optional(),
  take: z.number().optional(),
  skip: z.number().optional(),
  distinct: z.union([ FooScalarFieldEnumSchema,FooScalarFieldEnumSchema.array() ]).optional(),
}).strict() ;

export const FooFindManyArgsSchema: z.ZodType<Prisma.FooFindManyArgs> = z.object({
  select: FooSelectSchema.optional(),
  where: FooWhereInputSchema.optional(),
  orderBy: z.union([ FooOrderByWithRelationInputSchema.array(),FooOrderByWithRelationInputSchema ]).optional(),
  cursor: FooWhereUniqueInputSchema.optional(),
  take: z.number().optional(),
  skip: z.number().optional(),
  distinct: z.union([ FooScalarFieldEnumSchema,FooScalarFieldEnumSchema.array() ]).optional(),
}).strict() ;

export const FooAggregateArgsSchema: z.ZodType<Prisma.FooAggregateArgs> = z.object({
  where: FooWhereInputSchema.optional(),
  orderBy: z.union([ FooOrderByWithRelationInputSchema.array(),FooOrderByWithRelationInputSchema ]).optional(),
  cursor: FooWhereUniqueInputSchema.optional(),
  take: z.number().optional(),
  skip: z.number().optional(),
}).strict() ;

export const FooGroupByArgsSchema: z.ZodType<Prisma.FooGroupByArgs> = z.object({
  where: FooWhereInputSchema.optional(),
  orderBy: z.union([ FooOrderByWithAggregationInputSchema.array(),FooOrderByWithAggregationInputSchema ]).optional(),
  by: FooScalarFieldEnumSchema.array(),
  having: FooScalarWhereWithAggregatesInputSchema.optional(),
  take: z.number().optional(),
  skip: z.number().optional(),
}).strict() ;

export const FooFindUniqueArgsSchema: z.ZodType<Prisma.FooFindUniqueArgs> = z.object({
  select: FooSelectSchema.optional(),
  where: FooWhereUniqueInputSchema,
}).strict() ;

export const FooFindUniqueOrThrowArgsSchema: z.ZodType<Prisma.FooFindUniqueOrThrowArgs> = z.object({
  select: FooSelectSchema.optional(),
  where: FooWhereUniqueInputSchema,
}).strict() ;

export const TempEmbeddingsTableFindFirstArgsSchema: z.ZodType<Prisma.TempEmbeddingsTableFindFirstArgs> = z.object({
  select: TempEmbeddingsTableSelectSchema.optional(),
  where: TempEmbeddingsTableWhereInputSchema.optional(),
  orderBy: z.union([ TempEmbeddingsTableOrderByWithRelationInputSchema.array(),TempEmbeddingsTableOrderByWithRelationInputSchema ]).optional(),
  cursor: TempEmbeddingsTableWhereUniqueInputSchema.optional(),
  take: z.number().optional(),
  skip: z.number().optional(),
  distinct: z.union([ TempEmbeddingsTableScalarFieldEnumSchema,TempEmbeddingsTableScalarFieldEnumSchema.array() ]).optional(),
}).strict() ;

export const TempEmbeddingsTableFindFirstOrThrowArgsSchema: z.ZodType<Prisma.TempEmbeddingsTableFindFirstOrThrowArgs> = z.object({
  select: TempEmbeddingsTableSelectSchema.optional(),
  where: TempEmbeddingsTableWhereInputSchema.optional(),
  orderBy: z.union([ TempEmbeddingsTableOrderByWithRelationInputSchema.array(),TempEmbeddingsTableOrderByWithRelationInputSchema ]).optional(),
  cursor: TempEmbeddingsTableWhereUniqueInputSchema.optional(),
  take: z.number().optional(),
  skip: z.number().optional(),
  distinct: z.union([ TempEmbeddingsTableScalarFieldEnumSchema,TempEmbeddingsTableScalarFieldEnumSchema.array() ]).optional(),
}).strict() ;

export const TempEmbeddingsTableFindManyArgsSchema: z.ZodType<Prisma.TempEmbeddingsTableFindManyArgs> = z.object({
  select: TempEmbeddingsTableSelectSchema.optional(),
  where: TempEmbeddingsTableWhereInputSchema.optional(),
  orderBy: z.union([ TempEmbeddingsTableOrderByWithRelationInputSchema.array(),TempEmbeddingsTableOrderByWithRelationInputSchema ]).optional(),
  cursor: TempEmbeddingsTableWhereUniqueInputSchema.optional(),
  take: z.number().optional(),
  skip: z.number().optional(),
  distinct: z.union([ TempEmbeddingsTableScalarFieldEnumSchema,TempEmbeddingsTableScalarFieldEnumSchema.array() ]).optional(),
}).strict() ;

export const TempEmbeddingsTableAggregateArgsSchema: z.ZodType<Prisma.TempEmbeddingsTableAggregateArgs> = z.object({
  where: TempEmbeddingsTableWhereInputSchema.optional(),
  orderBy: z.union([ TempEmbeddingsTableOrderByWithRelationInputSchema.array(),TempEmbeddingsTableOrderByWithRelationInputSchema ]).optional(),
  cursor: TempEmbeddingsTableWhereUniqueInputSchema.optional(),
  take: z.number().optional(),
  skip: z.number().optional(),
}).strict() ;

export const TempEmbeddingsTableGroupByArgsSchema: z.ZodType<Prisma.TempEmbeddingsTableGroupByArgs> = z.object({
  where: TempEmbeddingsTableWhereInputSchema.optional(),
  orderBy: z.union([ TempEmbeddingsTableOrderByWithAggregationInputSchema.array(),TempEmbeddingsTableOrderByWithAggregationInputSchema ]).optional(),
  by: TempEmbeddingsTableScalarFieldEnumSchema.array(),
  having: TempEmbeddingsTableScalarWhereWithAggregatesInputSchema.optional(),
  take: z.number().optional(),
  skip: z.number().optional(),
}).strict() ;

export const TempEmbeddingsTableFindUniqueArgsSchema: z.ZodType<Prisma.TempEmbeddingsTableFindUniqueArgs> = z.object({
  select: TempEmbeddingsTableSelectSchema.optional(),
  where: TempEmbeddingsTableWhereUniqueInputSchema,
}).strict() ;

export const TempEmbeddingsTableFindUniqueOrThrowArgsSchema: z.ZodType<Prisma.TempEmbeddingsTableFindUniqueOrThrowArgs> = z.object({
  select: TempEmbeddingsTableSelectSchema.optional(),
  where: TempEmbeddingsTableWhereUniqueInputSchema,
}).strict() ;

export const UserFindFirstArgsSchema: z.ZodType<Prisma.UserFindFirstArgs> = z.object({
  select: UserSelectSchema.optional(),
  include: UserIncludeSchema.optional(),
  where: UserWhereInputSchema.optional(),
  orderBy: z.union([ UserOrderByWithRelationInputSchema.array(),UserOrderByWithRelationInputSchema ]).optional(),
  cursor: UserWhereUniqueInputSchema.optional(),
  take: z.number().optional(),
  skip: z.number().optional(),
  distinct: z.union([ UserScalarFieldEnumSchema,UserScalarFieldEnumSchema.array() ]).optional(),
}).strict() ;

export const UserFindFirstOrThrowArgsSchema: z.ZodType<Prisma.UserFindFirstOrThrowArgs> = z.object({
  select: UserSelectSchema.optional(),
  include: UserIncludeSchema.optional(),
  where: UserWhereInputSchema.optional(),
  orderBy: z.union([ UserOrderByWithRelationInputSchema.array(),UserOrderByWithRelationInputSchema ]).optional(),
  cursor: UserWhereUniqueInputSchema.optional(),
  take: z.number().optional(),
  skip: z.number().optional(),
  distinct: z.union([ UserScalarFieldEnumSchema,UserScalarFieldEnumSchema.array() ]).optional(),
}).strict() ;

export const UserFindManyArgsSchema: z.ZodType<Prisma.UserFindManyArgs> = z.object({
  select: UserSelectSchema.optional(),
  include: UserIncludeSchema.optional(),
  where: UserWhereInputSchema.optional(),
  orderBy: z.union([ UserOrderByWithRelationInputSchema.array(),UserOrderByWithRelationInputSchema ]).optional(),
  cursor: UserWhereUniqueInputSchema.optional(),
  take: z.number().optional(),
  skip: z.number().optional(),
  distinct: z.union([ UserScalarFieldEnumSchema,UserScalarFieldEnumSchema.array() ]).optional(),
}).strict() ;

export const UserAggregateArgsSchema: z.ZodType<Prisma.UserAggregateArgs> = z.object({
  where: UserWhereInputSchema.optional(),
  orderBy: z.union([ UserOrderByWithRelationInputSchema.array(),UserOrderByWithRelationInputSchema ]).optional(),
  cursor: UserWhereUniqueInputSchema.optional(),
  take: z.number().optional(),
  skip: z.number().optional(),
}).strict() ;

export const UserGroupByArgsSchema: z.ZodType<Prisma.UserGroupByArgs> = z.object({
  where: UserWhereInputSchema.optional(),
  orderBy: z.union([ UserOrderByWithAggregationInputSchema.array(),UserOrderByWithAggregationInputSchema ]).optional(),
  by: UserScalarFieldEnumSchema.array(),
  having: UserScalarWhereWithAggregatesInputSchema.optional(),
  take: z.number().optional(),
  skip: z.number().optional(),
}).strict() ;

export const UserFindUniqueArgsSchema: z.ZodType<Prisma.UserFindUniqueArgs> = z.object({
  select: UserSelectSchema.optional(),
  include: UserIncludeSchema.optional(),
  where: UserWhereUniqueInputSchema,
}).strict() ;

export const UserFindUniqueOrThrowArgsSchema: z.ZodType<Prisma.UserFindUniqueOrThrowArgs> = z.object({
  select: UserSelectSchema.optional(),
  include: UserIncludeSchema.optional(),
  where: UserWhereUniqueInputSchema,
}).strict() ;

export const KnowledgeNodeFindFirstArgsSchema: z.ZodType<Prisma.KnowledgeNodeFindFirstArgs> = z.object({
  select: KnowledgeNodeSelectSchema.optional(),
  include: KnowledgeNodeIncludeSchema.optional(),
  where: KnowledgeNodeWhereInputSchema.optional(),
  orderBy: z.union([ KnowledgeNodeOrderByWithRelationInputSchema.array(),KnowledgeNodeOrderByWithRelationInputSchema ]).optional(),
  cursor: KnowledgeNodeWhereUniqueInputSchema.optional(),
  take: z.number().optional(),
  skip: z.number().optional(),
  distinct: z.union([ KnowledgeNodeScalarFieldEnumSchema,KnowledgeNodeScalarFieldEnumSchema.array() ]).optional(),
}).strict() ;

export const KnowledgeNodeFindFirstOrThrowArgsSchema: z.ZodType<Prisma.KnowledgeNodeFindFirstOrThrowArgs> = z.object({
  select: KnowledgeNodeSelectSchema.optional(),
  include: KnowledgeNodeIncludeSchema.optional(),
  where: KnowledgeNodeWhereInputSchema.optional(),
  orderBy: z.union([ KnowledgeNodeOrderByWithRelationInputSchema.array(),KnowledgeNodeOrderByWithRelationInputSchema ]).optional(),
  cursor: KnowledgeNodeWhereUniqueInputSchema.optional(),
  take: z.number().optional(),
  skip: z.number().optional(),
  distinct: z.union([ KnowledgeNodeScalarFieldEnumSchema,KnowledgeNodeScalarFieldEnumSchema.array() ]).optional(),
}).strict() ;

export const KnowledgeNodeFindManyArgsSchema: z.ZodType<Prisma.KnowledgeNodeFindManyArgs> = z.object({
  select: KnowledgeNodeSelectSchema.optional(),
  include: KnowledgeNodeIncludeSchema.optional(),
  where: KnowledgeNodeWhereInputSchema.optional(),
  orderBy: z.union([ KnowledgeNodeOrderByWithRelationInputSchema.array(),KnowledgeNodeOrderByWithRelationInputSchema ]).optional(),
  cursor: KnowledgeNodeWhereUniqueInputSchema.optional(),
  take: z.number().optional(),
  skip: z.number().optional(),
  distinct: z.union([ KnowledgeNodeScalarFieldEnumSchema,KnowledgeNodeScalarFieldEnumSchema.array() ]).optional(),
}).strict() ;

export const KnowledgeNodeAggregateArgsSchema: z.ZodType<Prisma.KnowledgeNodeAggregateArgs> = z.object({
  where: KnowledgeNodeWhereInputSchema.optional(),
  orderBy: z.union([ KnowledgeNodeOrderByWithRelationInputSchema.array(),KnowledgeNodeOrderByWithRelationInputSchema ]).optional(),
  cursor: KnowledgeNodeWhereUniqueInputSchema.optional(),
  take: z.number().optional(),
  skip: z.number().optional(),
}).strict() ;

export const KnowledgeNodeGroupByArgsSchema: z.ZodType<Prisma.KnowledgeNodeGroupByArgs> = z.object({
  where: KnowledgeNodeWhereInputSchema.optional(),
  orderBy: z.union([ KnowledgeNodeOrderByWithAggregationInputSchema.array(),KnowledgeNodeOrderByWithAggregationInputSchema ]).optional(),
  by: KnowledgeNodeScalarFieldEnumSchema.array(),
  having: KnowledgeNodeScalarWhereWithAggregatesInputSchema.optional(),
  take: z.number().optional(),
  skip: z.number().optional(),
}).strict() ;

export const KnowledgeNodeFindUniqueArgsSchema: z.ZodType<Prisma.KnowledgeNodeFindUniqueArgs> = z.object({
  select: KnowledgeNodeSelectSchema.optional(),
  include: KnowledgeNodeIncludeSchema.optional(),
  where: KnowledgeNodeWhereUniqueInputSchema,
}).strict() ;

export const KnowledgeNodeFindUniqueOrThrowArgsSchema: z.ZodType<Prisma.KnowledgeNodeFindUniqueOrThrowArgs> = z.object({
  select: KnowledgeNodeSelectSchema.optional(),
  include: KnowledgeNodeIncludeSchema.optional(),
  where: KnowledgeNodeWhereUniqueInputSchema,
}).strict() ;

export const NodeRelationshipFindFirstArgsSchema: z.ZodType<Prisma.NodeRelationshipFindFirstArgs> = z.object({
  select: NodeRelationshipSelectSchema.optional(),
  include: NodeRelationshipIncludeSchema.optional(),
  where: NodeRelationshipWhereInputSchema.optional(),
  orderBy: z.union([ NodeRelationshipOrderByWithRelationInputSchema.array(),NodeRelationshipOrderByWithRelationInputSchema ]).optional(),
  cursor: NodeRelationshipWhereUniqueInputSchema.optional(),
  take: z.number().optional(),
  skip: z.number().optional(),
  distinct: z.union([ NodeRelationshipScalarFieldEnumSchema,NodeRelationshipScalarFieldEnumSchema.array() ]).optional(),
}).strict() ;

export const NodeRelationshipFindFirstOrThrowArgsSchema: z.ZodType<Prisma.NodeRelationshipFindFirstOrThrowArgs> = z.object({
  select: NodeRelationshipSelectSchema.optional(),
  include: NodeRelationshipIncludeSchema.optional(),
  where: NodeRelationshipWhereInputSchema.optional(),
  orderBy: z.union([ NodeRelationshipOrderByWithRelationInputSchema.array(),NodeRelationshipOrderByWithRelationInputSchema ]).optional(),
  cursor: NodeRelationshipWhereUniqueInputSchema.optional(),
  take: z.number().optional(),
  skip: z.number().optional(),
  distinct: z.union([ NodeRelationshipScalarFieldEnumSchema,NodeRelationshipScalarFieldEnumSchema.array() ]).optional(),
}).strict() ;

export const NodeRelationshipFindManyArgsSchema: z.ZodType<Prisma.NodeRelationshipFindManyArgs> = z.object({
  select: NodeRelationshipSelectSchema.optional(),
  include: NodeRelationshipIncludeSchema.optional(),
  where: NodeRelationshipWhereInputSchema.optional(),
  orderBy: z.union([ NodeRelationshipOrderByWithRelationInputSchema.array(),NodeRelationshipOrderByWithRelationInputSchema ]).optional(),
  cursor: NodeRelationshipWhereUniqueInputSchema.optional(),
  take: z.number().optional(),
  skip: z.number().optional(),
  distinct: z.union([ NodeRelationshipScalarFieldEnumSchema,NodeRelationshipScalarFieldEnumSchema.array() ]).optional(),
}).strict() ;

export const NodeRelationshipAggregateArgsSchema: z.ZodType<Prisma.NodeRelationshipAggregateArgs> = z.object({
  where: NodeRelationshipWhereInputSchema.optional(),
  orderBy: z.union([ NodeRelationshipOrderByWithRelationInputSchema.array(),NodeRelationshipOrderByWithRelationInputSchema ]).optional(),
  cursor: NodeRelationshipWhereUniqueInputSchema.optional(),
  take: z.number().optional(),
  skip: z.number().optional(),
}).strict() ;

export const NodeRelationshipGroupByArgsSchema: z.ZodType<Prisma.NodeRelationshipGroupByArgs> = z.object({
  where: NodeRelationshipWhereInputSchema.optional(),
  orderBy: z.union([ NodeRelationshipOrderByWithAggregationInputSchema.array(),NodeRelationshipOrderByWithAggregationInputSchema ]).optional(),
  by: NodeRelationshipScalarFieldEnumSchema.array(),
  having: NodeRelationshipScalarWhereWithAggregatesInputSchema.optional(),
  take: z.number().optional(),
  skip: z.number().optional(),
}).strict() ;

export const NodeRelationshipFindUniqueArgsSchema: z.ZodType<Prisma.NodeRelationshipFindUniqueArgs> = z.object({
  select: NodeRelationshipSelectSchema.optional(),
  include: NodeRelationshipIncludeSchema.optional(),
  where: NodeRelationshipWhereUniqueInputSchema,
}).strict() ;

export const NodeRelationshipFindUniqueOrThrowArgsSchema: z.ZodType<Prisma.NodeRelationshipFindUniqueOrThrowArgs> = z.object({
  select: NodeRelationshipSelectSchema.optional(),
  include: NodeRelationshipIncludeSchema.optional(),
  where: NodeRelationshipWhereUniqueInputSchema,
}).strict() ;

export const ValidationFindFirstArgsSchema: z.ZodType<Prisma.ValidationFindFirstArgs> = z.object({
  select: ValidationSelectSchema.optional(),
  include: ValidationIncludeSchema.optional(),
  where: ValidationWhereInputSchema.optional(),
  orderBy: z.union([ ValidationOrderByWithRelationInputSchema.array(),ValidationOrderByWithRelationInputSchema ]).optional(),
  cursor: ValidationWhereUniqueInputSchema.optional(),
  take: z.number().optional(),
  skip: z.number().optional(),
  distinct: z.union([ ValidationScalarFieldEnumSchema,ValidationScalarFieldEnumSchema.array() ]).optional(),
}).strict() ;

export const ValidationFindFirstOrThrowArgsSchema: z.ZodType<Prisma.ValidationFindFirstOrThrowArgs> = z.object({
  select: ValidationSelectSchema.optional(),
  include: ValidationIncludeSchema.optional(),
  where: ValidationWhereInputSchema.optional(),
  orderBy: z.union([ ValidationOrderByWithRelationInputSchema.array(),ValidationOrderByWithRelationInputSchema ]).optional(),
  cursor: ValidationWhereUniqueInputSchema.optional(),
  take: z.number().optional(),
  skip: z.number().optional(),
  distinct: z.union([ ValidationScalarFieldEnumSchema,ValidationScalarFieldEnumSchema.array() ]).optional(),
}).strict() ;

export const ValidationFindManyArgsSchema: z.ZodType<Prisma.ValidationFindManyArgs> = z.object({
  select: ValidationSelectSchema.optional(),
  include: ValidationIncludeSchema.optional(),
  where: ValidationWhereInputSchema.optional(),
  orderBy: z.union([ ValidationOrderByWithRelationInputSchema.array(),ValidationOrderByWithRelationInputSchema ]).optional(),
  cursor: ValidationWhereUniqueInputSchema.optional(),
  take: z.number().optional(),
  skip: z.number().optional(),
  distinct: z.union([ ValidationScalarFieldEnumSchema,ValidationScalarFieldEnumSchema.array() ]).optional(),
}).strict() ;

export const ValidationAggregateArgsSchema: z.ZodType<Prisma.ValidationAggregateArgs> = z.object({
  where: ValidationWhereInputSchema.optional(),
  orderBy: z.union([ ValidationOrderByWithRelationInputSchema.array(),ValidationOrderByWithRelationInputSchema ]).optional(),
  cursor: ValidationWhereUniqueInputSchema.optional(),
  take: z.number().optional(),
  skip: z.number().optional(),
}).strict() ;

export const ValidationGroupByArgsSchema: z.ZodType<Prisma.ValidationGroupByArgs> = z.object({
  where: ValidationWhereInputSchema.optional(),
  orderBy: z.union([ ValidationOrderByWithAggregationInputSchema.array(),ValidationOrderByWithAggregationInputSchema ]).optional(),
  by: ValidationScalarFieldEnumSchema.array(),
  having: ValidationScalarWhereWithAggregatesInputSchema.optional(),
  take: z.number().optional(),
  skip: z.number().optional(),
}).strict() ;

export const ValidationFindUniqueArgsSchema: z.ZodType<Prisma.ValidationFindUniqueArgs> = z.object({
  select: ValidationSelectSchema.optional(),
  include: ValidationIncludeSchema.optional(),
  where: ValidationWhereUniqueInputSchema,
}).strict() ;

export const ValidationFindUniqueOrThrowArgsSchema: z.ZodType<Prisma.ValidationFindUniqueOrThrowArgs> = z.object({
  select: ValidationSelectSchema.optional(),
  include: ValidationIncludeSchema.optional(),
  where: ValidationWhereUniqueInputSchema,
}).strict() ;

export const TopicFindFirstArgsSchema: z.ZodType<Prisma.TopicFindFirstArgs> = z.object({
  select: TopicSelectSchema.optional(),
  include: TopicIncludeSchema.optional(),
  where: TopicWhereInputSchema.optional(),
  orderBy: z.union([ TopicOrderByWithRelationInputSchema.array(),TopicOrderByWithRelationInputSchema ]).optional(),
  cursor: TopicWhereUniqueInputSchema.optional(),
  take: z.number().optional(),
  skip: z.number().optional(),
  distinct: z.union([ TopicScalarFieldEnumSchema,TopicScalarFieldEnumSchema.array() ]).optional(),
}).strict() ;

export const TopicFindFirstOrThrowArgsSchema: z.ZodType<Prisma.TopicFindFirstOrThrowArgs> = z.object({
  select: TopicSelectSchema.optional(),
  include: TopicIncludeSchema.optional(),
  where: TopicWhereInputSchema.optional(),
  orderBy: z.union([ TopicOrderByWithRelationInputSchema.array(),TopicOrderByWithRelationInputSchema ]).optional(),
  cursor: TopicWhereUniqueInputSchema.optional(),
  take: z.number().optional(),
  skip: z.number().optional(),
  distinct: z.union([ TopicScalarFieldEnumSchema,TopicScalarFieldEnumSchema.array() ]).optional(),
}).strict() ;

export const TopicFindManyArgsSchema: z.ZodType<Prisma.TopicFindManyArgs> = z.object({
  select: TopicSelectSchema.optional(),
  include: TopicIncludeSchema.optional(),
  where: TopicWhereInputSchema.optional(),
  orderBy: z.union([ TopicOrderByWithRelationInputSchema.array(),TopicOrderByWithRelationInputSchema ]).optional(),
  cursor: TopicWhereUniqueInputSchema.optional(),
  take: z.number().optional(),
  skip: z.number().optional(),
  distinct: z.union([ TopicScalarFieldEnumSchema,TopicScalarFieldEnumSchema.array() ]).optional(),
}).strict() ;

export const TopicAggregateArgsSchema: z.ZodType<Prisma.TopicAggregateArgs> = z.object({
  where: TopicWhereInputSchema.optional(),
  orderBy: z.union([ TopicOrderByWithRelationInputSchema.array(),TopicOrderByWithRelationInputSchema ]).optional(),
  cursor: TopicWhereUniqueInputSchema.optional(),
  take: z.number().optional(),
  skip: z.number().optional(),
}).strict() ;

export const TopicGroupByArgsSchema: z.ZodType<Prisma.TopicGroupByArgs> = z.object({
  where: TopicWhereInputSchema.optional(),
  orderBy: z.union([ TopicOrderByWithAggregationInputSchema.array(),TopicOrderByWithAggregationInputSchema ]).optional(),
  by: TopicScalarFieldEnumSchema.array(),
  having: TopicScalarWhereWithAggregatesInputSchema.optional(),
  take: z.number().optional(),
  skip: z.number().optional(),
}).strict() ;

export const TopicFindUniqueArgsSchema: z.ZodType<Prisma.TopicFindUniqueArgs> = z.object({
  select: TopicSelectSchema.optional(),
  include: TopicIncludeSchema.optional(),
  where: TopicWhereUniqueInputSchema,
}).strict() ;

export const TopicFindUniqueOrThrowArgsSchema: z.ZodType<Prisma.TopicFindUniqueOrThrowArgs> = z.object({
  select: TopicSelectSchema.optional(),
  include: TopicIncludeSchema.optional(),
  where: TopicWhereUniqueInputSchema,
}).strict() ;

export const FooCreateArgsSchema: z.ZodType<Prisma.FooCreateArgs> = z.object({
  select: FooSelectSchema.optional(),
  data: z.union([ FooCreateInputSchema,FooUncheckedCreateInputSchema ]),
}).strict() ;

export const FooUpsertArgsSchema: z.ZodType<Prisma.FooUpsertArgs> = z.object({
  select: FooSelectSchema.optional(),
  where: FooWhereUniqueInputSchema,
  create: z.union([ FooCreateInputSchema,FooUncheckedCreateInputSchema ]),
  update: z.union([ FooUpdateInputSchema,FooUncheckedUpdateInputSchema ]),
}).strict() ;

export const FooCreateManyArgsSchema: z.ZodType<Prisma.FooCreateManyArgs> = z.object({
  data: z.union([ FooCreateManyInputSchema,FooCreateManyInputSchema.array() ]),
  skipDuplicates: z.boolean().optional(),
}).strict() ;

export const FooCreateManyAndReturnArgsSchema: z.ZodType<Prisma.FooCreateManyAndReturnArgs> = z.object({
  data: z.union([ FooCreateManyInputSchema,FooCreateManyInputSchema.array() ]),
  skipDuplicates: z.boolean().optional(),
}).strict() ;

export const FooDeleteArgsSchema: z.ZodType<Prisma.FooDeleteArgs> = z.object({
  select: FooSelectSchema.optional(),
  where: FooWhereUniqueInputSchema,
}).strict() ;

export const FooUpdateArgsSchema: z.ZodType<Prisma.FooUpdateArgs> = z.object({
  select: FooSelectSchema.optional(),
  data: z.union([ FooUpdateInputSchema,FooUncheckedUpdateInputSchema ]),
  where: FooWhereUniqueInputSchema,
}).strict() ;

export const FooUpdateManyArgsSchema: z.ZodType<Prisma.FooUpdateManyArgs> = z.object({
  data: z.union([ FooUpdateManyMutationInputSchema,FooUncheckedUpdateManyInputSchema ]),
  where: FooWhereInputSchema.optional(),
  limit: z.number().optional(),
}).strict() ;

export const FooUpdateManyAndReturnArgsSchema: z.ZodType<Prisma.FooUpdateManyAndReturnArgs> = z.object({
  data: z.union([ FooUpdateManyMutationInputSchema,FooUncheckedUpdateManyInputSchema ]),
  where: FooWhereInputSchema.optional(),
  limit: z.number().optional(),
}).strict() ;

export const FooDeleteManyArgsSchema: z.ZodType<Prisma.FooDeleteManyArgs> = z.object({
  where: FooWhereInputSchema.optional(),
  limit: z.number().optional(),
}).strict() ;

export const TempEmbeddingsTableDeleteArgsSchema: z.ZodType<Prisma.TempEmbeddingsTableDeleteArgs> = z.object({
  select: TempEmbeddingsTableSelectSchema.optional(),
  where: TempEmbeddingsTableWhereUniqueInputSchema,
}).strict() ;

export const TempEmbeddingsTableUpdateArgsSchema: z.ZodType<Prisma.TempEmbeddingsTableUpdateArgs> = z.object({
  select: TempEmbeddingsTableSelectSchema.optional(),
  data: z.union([ TempEmbeddingsTableUpdateInputSchema,TempEmbeddingsTableUncheckedUpdateInputSchema ]),
  where: TempEmbeddingsTableWhereUniqueInputSchema,
}).strict() ;

export const TempEmbeddingsTableUpdateManyArgsSchema: z.ZodType<Prisma.TempEmbeddingsTableUpdateManyArgs> = z.object({
  data: z.union([ TempEmbeddingsTableUpdateManyMutationInputSchema,TempEmbeddingsTableUncheckedUpdateManyInputSchema ]),
  where: TempEmbeddingsTableWhereInputSchema.optional(),
  limit: z.number().optional(),
}).strict() ;

export const TempEmbeddingsTableUpdateManyAndReturnArgsSchema: z.ZodType<Prisma.TempEmbeddingsTableUpdateManyAndReturnArgs> = z.object({
  data: z.union([ TempEmbeddingsTableUpdateManyMutationInputSchema,TempEmbeddingsTableUncheckedUpdateManyInputSchema ]),
  where: TempEmbeddingsTableWhereInputSchema.optional(),
  limit: z.number().optional(),
}).strict() ;

export const TempEmbeddingsTableDeleteManyArgsSchema: z.ZodType<Prisma.TempEmbeddingsTableDeleteManyArgs> = z.object({
  where: TempEmbeddingsTableWhereInputSchema.optional(),
  limit: z.number().optional(),
}).strict() ;

export const UserCreateArgsSchema: z.ZodType<Prisma.UserCreateArgs> = z.object({
  select: UserSelectSchema.optional(),
  include: UserIncludeSchema.optional(),
  data: z.union([ UserCreateInputSchema,UserUncheckedCreateInputSchema ]),
}).strict() ;

export const UserUpsertArgsSchema: z.ZodType<Prisma.UserUpsertArgs> = z.object({
  select: UserSelectSchema.optional(),
  include: UserIncludeSchema.optional(),
  where: UserWhereUniqueInputSchema,
  create: z.union([ UserCreateInputSchema,UserUncheckedCreateInputSchema ]),
  update: z.union([ UserUpdateInputSchema,UserUncheckedUpdateInputSchema ]),
}).strict() ;

export const UserCreateManyArgsSchema: z.ZodType<Prisma.UserCreateManyArgs> = z.object({
  data: z.union([ UserCreateManyInputSchema,UserCreateManyInputSchema.array() ]),
  skipDuplicates: z.boolean().optional(),
}).strict() ;

export const UserCreateManyAndReturnArgsSchema: z.ZodType<Prisma.UserCreateManyAndReturnArgs> = z.object({
  data: z.union([ UserCreateManyInputSchema,UserCreateManyInputSchema.array() ]),
  skipDuplicates: z.boolean().optional(),
}).strict() ;

export const UserDeleteArgsSchema: z.ZodType<Prisma.UserDeleteArgs> = z.object({
  select: UserSelectSchema.optional(),
  include: UserIncludeSchema.optional(),
  where: UserWhereUniqueInputSchema,
}).strict() ;

export const UserUpdateArgsSchema: z.ZodType<Prisma.UserUpdateArgs> = z.object({
  select: UserSelectSchema.optional(),
  include: UserIncludeSchema.optional(),
  data: z.union([ UserUpdateInputSchema,UserUncheckedUpdateInputSchema ]),
  where: UserWhereUniqueInputSchema,
}).strict() ;

export const UserUpdateManyArgsSchema: z.ZodType<Prisma.UserUpdateManyArgs> = z.object({
  data: z.union([ UserUpdateManyMutationInputSchema,UserUncheckedUpdateManyInputSchema ]),
  where: UserWhereInputSchema.optional(),
  limit: z.number().optional(),
}).strict() ;

export const UserUpdateManyAndReturnArgsSchema: z.ZodType<Prisma.UserUpdateManyAndReturnArgs> = z.object({
  data: z.union([ UserUpdateManyMutationInputSchema,UserUncheckedUpdateManyInputSchema ]),
  where: UserWhereInputSchema.optional(),
  limit: z.number().optional(),
}).strict() ;

export const UserDeleteManyArgsSchema: z.ZodType<Prisma.UserDeleteManyArgs> = z.object({
  where: UserWhereInputSchema.optional(),
  limit: z.number().optional(),
}).strict() ;

export const KnowledgeNodeCreateArgsSchema: z.ZodType<Prisma.KnowledgeNodeCreateArgs> = z.object({
  select: KnowledgeNodeSelectSchema.optional(),
  include: KnowledgeNodeIncludeSchema.optional(),
  data: z.union([ KnowledgeNodeCreateInputSchema,KnowledgeNodeUncheckedCreateInputSchema ]),
}).strict() ;

export const KnowledgeNodeUpsertArgsSchema: z.ZodType<Prisma.KnowledgeNodeUpsertArgs> = z.object({
  select: KnowledgeNodeSelectSchema.optional(),
  include: KnowledgeNodeIncludeSchema.optional(),
  where: KnowledgeNodeWhereUniqueInputSchema,
  create: z.union([ KnowledgeNodeCreateInputSchema,KnowledgeNodeUncheckedCreateInputSchema ]),
  update: z.union([ KnowledgeNodeUpdateInputSchema,KnowledgeNodeUncheckedUpdateInputSchema ]),
}).strict() ;

export const KnowledgeNodeCreateManyArgsSchema: z.ZodType<Prisma.KnowledgeNodeCreateManyArgs> = z.object({
  data: z.union([ KnowledgeNodeCreateManyInputSchema,KnowledgeNodeCreateManyInputSchema.array() ]),
  skipDuplicates: z.boolean().optional(),
}).strict() ;

export const KnowledgeNodeCreateManyAndReturnArgsSchema: z.ZodType<Prisma.KnowledgeNodeCreateManyAndReturnArgs> = z.object({
  data: z.union([ KnowledgeNodeCreateManyInputSchema,KnowledgeNodeCreateManyInputSchema.array() ]),
  skipDuplicates: z.boolean().optional(),
}).strict() ;

export const KnowledgeNodeDeleteArgsSchema: z.ZodType<Prisma.KnowledgeNodeDeleteArgs> = z.object({
  select: KnowledgeNodeSelectSchema.optional(),
  include: KnowledgeNodeIncludeSchema.optional(),
  where: KnowledgeNodeWhereUniqueInputSchema,
}).strict() ;

export const KnowledgeNodeUpdateArgsSchema: z.ZodType<Prisma.KnowledgeNodeUpdateArgs> = z.object({
  select: KnowledgeNodeSelectSchema.optional(),
  include: KnowledgeNodeIncludeSchema.optional(),
  data: z.union([ KnowledgeNodeUpdateInputSchema,KnowledgeNodeUncheckedUpdateInputSchema ]),
  where: KnowledgeNodeWhereUniqueInputSchema,
}).strict() ;

export const KnowledgeNodeUpdateManyArgsSchema: z.ZodType<Prisma.KnowledgeNodeUpdateManyArgs> = z.object({
  data: z.union([ KnowledgeNodeUpdateManyMutationInputSchema,KnowledgeNodeUncheckedUpdateManyInputSchema ]),
  where: KnowledgeNodeWhereInputSchema.optional(),
  limit: z.number().optional(),
}).strict() ;

export const KnowledgeNodeUpdateManyAndReturnArgsSchema: z.ZodType<Prisma.KnowledgeNodeUpdateManyAndReturnArgs> = z.object({
  data: z.union([ KnowledgeNodeUpdateManyMutationInputSchema,KnowledgeNodeUncheckedUpdateManyInputSchema ]),
  where: KnowledgeNodeWhereInputSchema.optional(),
  limit: z.number().optional(),
}).strict() ;

export const KnowledgeNodeDeleteManyArgsSchema: z.ZodType<Prisma.KnowledgeNodeDeleteManyArgs> = z.object({
  where: KnowledgeNodeWhereInputSchema.optional(),
  limit: z.number().optional(),
}).strict() ;

export const NodeRelationshipCreateArgsSchema: z.ZodType<Prisma.NodeRelationshipCreateArgs> = z.object({
  select: NodeRelationshipSelectSchema.optional(),
  include: NodeRelationshipIncludeSchema.optional(),
  data: z.union([ NodeRelationshipCreateInputSchema,NodeRelationshipUncheckedCreateInputSchema ]),
}).strict() ;

export const NodeRelationshipUpsertArgsSchema: z.ZodType<Prisma.NodeRelationshipUpsertArgs> = z.object({
  select: NodeRelationshipSelectSchema.optional(),
  include: NodeRelationshipIncludeSchema.optional(),
  where: NodeRelationshipWhereUniqueInputSchema,
  create: z.union([ NodeRelationshipCreateInputSchema,NodeRelationshipUncheckedCreateInputSchema ]),
  update: z.union([ NodeRelationshipUpdateInputSchema,NodeRelationshipUncheckedUpdateInputSchema ]),
}).strict() ;

export const NodeRelationshipCreateManyArgsSchema: z.ZodType<Prisma.NodeRelationshipCreateManyArgs> = z.object({
  data: z.union([ NodeRelationshipCreateManyInputSchema,NodeRelationshipCreateManyInputSchema.array() ]),
  skipDuplicates: z.boolean().optional(),
}).strict() ;

export const NodeRelationshipCreateManyAndReturnArgsSchema: z.ZodType<Prisma.NodeRelationshipCreateManyAndReturnArgs> = z.object({
  data: z.union([ NodeRelationshipCreateManyInputSchema,NodeRelationshipCreateManyInputSchema.array() ]),
  skipDuplicates: z.boolean().optional(),
}).strict() ;

export const NodeRelationshipDeleteArgsSchema: z.ZodType<Prisma.NodeRelationshipDeleteArgs> = z.object({
  select: NodeRelationshipSelectSchema.optional(),
  include: NodeRelationshipIncludeSchema.optional(),
  where: NodeRelationshipWhereUniqueInputSchema,
}).strict() ;

export const NodeRelationshipUpdateArgsSchema: z.ZodType<Prisma.NodeRelationshipUpdateArgs> = z.object({
  select: NodeRelationshipSelectSchema.optional(),
  include: NodeRelationshipIncludeSchema.optional(),
  data: z.union([ NodeRelationshipUpdateInputSchema,NodeRelationshipUncheckedUpdateInputSchema ]),
  where: NodeRelationshipWhereUniqueInputSchema,
}).strict() ;

export const NodeRelationshipUpdateManyArgsSchema: z.ZodType<Prisma.NodeRelationshipUpdateManyArgs> = z.object({
  data: z.union([ NodeRelationshipUpdateManyMutationInputSchema,NodeRelationshipUncheckedUpdateManyInputSchema ]),
  where: NodeRelationshipWhereInputSchema.optional(),
  limit: z.number().optional(),
}).strict() ;

export const NodeRelationshipUpdateManyAndReturnArgsSchema: z.ZodType<Prisma.NodeRelationshipUpdateManyAndReturnArgs> = z.object({
  data: z.union([ NodeRelationshipUpdateManyMutationInputSchema,NodeRelationshipUncheckedUpdateManyInputSchema ]),
  where: NodeRelationshipWhereInputSchema.optional(),
  limit: z.number().optional(),
}).strict() ;

export const NodeRelationshipDeleteManyArgsSchema: z.ZodType<Prisma.NodeRelationshipDeleteManyArgs> = z.object({
  where: NodeRelationshipWhereInputSchema.optional(),
  limit: z.number().optional(),
}).strict() ;

export const ValidationCreateArgsSchema: z.ZodType<Prisma.ValidationCreateArgs> = z.object({
  select: ValidationSelectSchema.optional(),
  include: ValidationIncludeSchema.optional(),
  data: z.union([ ValidationCreateInputSchema,ValidationUncheckedCreateInputSchema ]),
}).strict() ;

export const ValidationUpsertArgsSchema: z.ZodType<Prisma.ValidationUpsertArgs> = z.object({
  select: ValidationSelectSchema.optional(),
  include: ValidationIncludeSchema.optional(),
  where: ValidationWhereUniqueInputSchema,
  create: z.union([ ValidationCreateInputSchema,ValidationUncheckedCreateInputSchema ]),
  update: z.union([ ValidationUpdateInputSchema,ValidationUncheckedUpdateInputSchema ]),
}).strict() ;

export const ValidationCreateManyArgsSchema: z.ZodType<Prisma.ValidationCreateManyArgs> = z.object({
  data: z.union([ ValidationCreateManyInputSchema,ValidationCreateManyInputSchema.array() ]),
  skipDuplicates: z.boolean().optional(),
}).strict() ;

export const ValidationCreateManyAndReturnArgsSchema: z.ZodType<Prisma.ValidationCreateManyAndReturnArgs> = z.object({
  data: z.union([ ValidationCreateManyInputSchema,ValidationCreateManyInputSchema.array() ]),
  skipDuplicates: z.boolean().optional(),
}).strict() ;

export const ValidationDeleteArgsSchema: z.ZodType<Prisma.ValidationDeleteArgs> = z.object({
  select: ValidationSelectSchema.optional(),
  include: ValidationIncludeSchema.optional(),
  where: ValidationWhereUniqueInputSchema,
}).strict() ;

export const ValidationUpdateArgsSchema: z.ZodType<Prisma.ValidationUpdateArgs> = z.object({
  select: ValidationSelectSchema.optional(),
  include: ValidationIncludeSchema.optional(),
  data: z.union([ ValidationUpdateInputSchema,ValidationUncheckedUpdateInputSchema ]),
  where: ValidationWhereUniqueInputSchema,
}).strict() ;

export const ValidationUpdateManyArgsSchema: z.ZodType<Prisma.ValidationUpdateManyArgs> = z.object({
  data: z.union([ ValidationUpdateManyMutationInputSchema,ValidationUncheckedUpdateManyInputSchema ]),
  where: ValidationWhereInputSchema.optional(),
  limit: z.number().optional(),
}).strict() ;

export const ValidationUpdateManyAndReturnArgsSchema: z.ZodType<Prisma.ValidationUpdateManyAndReturnArgs> = z.object({
  data: z.union([ ValidationUpdateManyMutationInputSchema,ValidationUncheckedUpdateManyInputSchema ]),
  where: ValidationWhereInputSchema.optional(),
  limit: z.number().optional(),
}).strict() ;

export const ValidationDeleteManyArgsSchema: z.ZodType<Prisma.ValidationDeleteManyArgs> = z.object({
  where: ValidationWhereInputSchema.optional(),
  limit: z.number().optional(),
}).strict() ;

export const TopicCreateArgsSchema: z.ZodType<Prisma.TopicCreateArgs> = z.object({
  select: TopicSelectSchema.optional(),
  include: TopicIncludeSchema.optional(),
  data: z.union([ TopicCreateInputSchema,TopicUncheckedCreateInputSchema ]),
}).strict() ;

export const TopicUpsertArgsSchema: z.ZodType<Prisma.TopicUpsertArgs> = z.object({
  select: TopicSelectSchema.optional(),
  include: TopicIncludeSchema.optional(),
  where: TopicWhereUniqueInputSchema,
  create: z.union([ TopicCreateInputSchema,TopicUncheckedCreateInputSchema ]),
  update: z.union([ TopicUpdateInputSchema,TopicUncheckedUpdateInputSchema ]),
}).strict() ;

export const TopicCreateManyArgsSchema: z.ZodType<Prisma.TopicCreateManyArgs> = z.object({
  data: z.union([ TopicCreateManyInputSchema,TopicCreateManyInputSchema.array() ]),
  skipDuplicates: z.boolean().optional(),
}).strict() ;

export const TopicCreateManyAndReturnArgsSchema: z.ZodType<Prisma.TopicCreateManyAndReturnArgs> = z.object({
  data: z.union([ TopicCreateManyInputSchema,TopicCreateManyInputSchema.array() ]),
  skipDuplicates: z.boolean().optional(),
}).strict() ;

export const TopicDeleteArgsSchema: z.ZodType<Prisma.TopicDeleteArgs> = z.object({
  select: TopicSelectSchema.optional(),
  include: TopicIncludeSchema.optional(),
  where: TopicWhereUniqueInputSchema,
}).strict() ;

export const TopicUpdateArgsSchema: z.ZodType<Prisma.TopicUpdateArgs> = z.object({
  select: TopicSelectSchema.optional(),
  include: TopicIncludeSchema.optional(),
  data: z.union([ TopicUpdateInputSchema,TopicUncheckedUpdateInputSchema ]),
  where: TopicWhereUniqueInputSchema,
}).strict() ;

export const TopicUpdateManyArgsSchema: z.ZodType<Prisma.TopicUpdateManyArgs> = z.object({
  data: z.union([ TopicUpdateManyMutationInputSchema,TopicUncheckedUpdateManyInputSchema ]),
  where: TopicWhereInputSchema.optional(),
  limit: z.number().optional(),
}).strict() ;

export const TopicUpdateManyAndReturnArgsSchema: z.ZodType<Prisma.TopicUpdateManyAndReturnArgs> = z.object({
  data: z.union([ TopicUpdateManyMutationInputSchema,TopicUncheckedUpdateManyInputSchema ]),
  where: TopicWhereInputSchema.optional(),
  limit: z.number().optional(),
}).strict() ;

export const TopicDeleteManyArgsSchema: z.ZodType<Prisma.TopicDeleteManyArgs> = z.object({
  where: TopicWhereInputSchema.optional(),
  limit: z.number().optional(),
}).strict() ;