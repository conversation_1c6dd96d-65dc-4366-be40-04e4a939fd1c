generator client {
  provider        = "prisma-client-js"
  output          = "../generated/client"
  previewFeatures = ["multiSchema"]
}

generator zod {
  provider = "zod-prisma-types"
  output   = "../../entities/src"
}

datasource db {
  provider  = "postgresql"
  url       = env("DATABASE_URL")
  directUrl = env("DIRECT_URL")
  schemas   = ["public", "extensions"]
}

// extensions schema - has PGVector installed
// anything with vector columns needs to go here
model TempEmbeddingsTable {
  id String @id @default(cuid())

  parentId String?
  type     String?
  text     String?
  metadata Json?

  embedding Unsupported("extensions.vector(1536)")

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@schema("extensions")
}

model KnowledgeNodeEmbeddings {
  id               String                                 @id @default(cuid())
  nodeId           String                                 @unique
  contentEmbedding Unsupported("extensions.vector(1536)")

  @@schema("extensions")
}

// public schema - no PGVector
model Foo {
  id    Int    @id @default(autoincrement())
  name  String @unique
  email String @unique

  @@schema("public")
}

model User {
  userId      String          @id
  nodes       KnowledgeNode[] @relation("Author")
  validations Validation[]

  @@schema("public")
}

model KnowledgeNode {
  id        String     @id @default(cuid())
  content   String
  type      NodeType
  source    SourceType
  context   Json?
  validated Boolean    @default(false)

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  authorId String
  author   User   @relation("Author", fields: [authorId], references: [userId])

  relationshipsFrom NodeRelationship[] @relation("FromNode")
  relationshipsTo   NodeRelationship[] @relation("ToNode")

  validations Validation[]
  topics      Topic[]

  @@schema("public")
}

model NodeRelationship {
  id     String           @id @default(cuid())
  fromId String
  toId   String
  type   RelationshipType

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  from KnowledgeNode @relation("FromNode", fields: [fromId], references: [id])
  to   KnowledgeNode @relation("ToNode", fields: [toId], references: [id])

  @@schema("public")
}

model Validation {
  id       String  @id @default(cuid())
  nodeId   String
  expertId String
  notes    String?

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  node   KnowledgeNode @relation(fields: [nodeId], references: [id])
  expert User          @relation(fields: [expertId], references: [userId])

  @@schema("public")
}

model Topic {
  id   String @id @default(cuid())
  name String @unique

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  nodes KnowledgeNode[]

  @@schema("public")
}

enum SourceType {
  slack
  notion

  @@schema("public")
}

enum NodeType {
  fact
  decision
  definition
  question
  answer

  @@schema("public")
}

enum RelationshipType {
  answers
  contradicts
  supports
  updates
  authored_by
  relates_to_topic
  validated_by

  @@schema("public")
}

// explorations into topic-specific agents 👇
/// --------------------------------------------
/// MODELS
/// --------------------------------------------

model Agent {
  id          String   @id @default(cuid())
  name        String
  description String?
  povSummary  String?
  ownerId     String
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  qnaPairs QnAPair[]
  sessions Session[]

  @@schema("public")
}

model QnAPair {
  id        String      @id @default(cuid())
  agentId   String
  question  String
  answer    String
  persona   PersonaType
  createdAt DateTime    @default(now())

  agent Agent @relation(fields: [agentId], references: [id])

  @@schema("public")
}

model QnAPairEmbedding {
  id        String                                 @id @default(cuid())
  qnaPairId String
  vector    Unsupported("extensions.vector(1536)")
  model     String
  createdAt DateTime                               @default(now())

  @@schema("extensions")
}

model Session {
  id        String        @id @default(cuid())
  agentId   String
  status    SessionStatus @default(ACTIVE)
  createdAt DateTime      @default(now())
  updatedAt DateTime      @updatedAt

  agent        Agent                @relation(fields: [agentId], references: [id])
  messages     SessionMessage[]
  participants SessionParticipant[]

  @@schema("public")
}

model SessionMessage {
  id        String   @id @default(cuid())
  sessionId String
  senderId  String? // optional: if it's a user message
  role      RoleType // AGENT or USER
  content   String
  createdAt DateTime @default(now())

  session Session @relation(fields: [sessionId], references: [id])

  @@schema("public")
}

model SessionParticipant {
  id        String      @id @default(cuid())
  sessionId String
  userId    String
  role      SessionRole // e.g., OWNER, PARTICIPANT
  joinedAt  DateTime    @default(now())

  session Session @relation(fields: [sessionId], references: [id])

  @@schema("public")
}

/// --------------------------------------------
/// ENUMS
/// --------------------------------------------

enum PersonaType {
  FINANCE
  PRODUCT
  MARKETING
  CEO
  SALES
  ENGINEERING

  @@schema("public")
}

enum SessionStatus {
  ACTIVE
  CLOSED

  @@schema("public")
}

enum RoleType {
  USER
  AGENT

  @@schema("public")
}

enum SessionRole {
  OWNER // The expert who created the agent
  PARTICIPANT // Anyone else joining the session

  @@schema("public")
}
