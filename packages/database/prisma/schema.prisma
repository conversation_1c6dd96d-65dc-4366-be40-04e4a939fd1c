generator client {
  provider = "prisma-client-js"
  output   = "../generated/client"
}

generator zod {
  provider = "zod-prisma-types"
  output   = "../../entities/src"
}

datasource db {
  provider  = "postgresql"
  url       = env("DATABASE_URL")
  directUrl = env("DIRECT_URL")
}

model Foo {
  id    Int    @id @default(autoincrement())
  name  String @unique
  email String @unique
}

model TempEmbeddingsTable {
  id String @id @default(cuid())

  parentId String?
  type     String?
  text     String?
  metadata Json?

  embedding Float[]

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
}

model User {
  userId      String          @id
  nodes       KnowledgeNode[] @relation("Author")
  validations Validation[]
}

model KnowledgeNode {
  id        String     @id @default(cuid())
  content   String
  embedding Float[]
  type      NodeType
  source    SourceType
  context   Json?
  validated Boolean    @default(false)

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  authorId String
  author   User   @relation("Author", fields: [authorId], references: [userId])

  relationshipsFrom NodeRelationship[] @relation("FromNode")
  relationshipsTo   NodeRelationship[] @relation("ToNode")

  validations Validation[]
  topics      Topic[]
}

model NodeRelationship {
  id     String           @id @default(cuid())
  fromId String
  toId   String
  type   RelationshipType

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  from KnowledgeNode @relation("FromNode", fields: [fromId], references: [id])
  to   KnowledgeNode @relation("ToNode", fields: [toId], references: [id])
}

model Validation {
  id       String  @id @default(cuid())
  nodeId   String
  expertId String
  notes    String?

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  node   KnowledgeNode @relation(fields: [nodeId], references: [id])
  expert User          @relation(fields: [expertId], references: [userId])
}

model Topic {
  id   String @id @default(cuid())
  name String @unique

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  nodes KnowledgeNode[]
}

enum SourceType {
  slack
  notion
}

enum NodeType {
  fact
  decision
  definition
  question
  answer
}

enum RelationshipType {
  answers
  contradicts
  supports
  updates
  authored_by
  relates_to_topic
  validated_by
}
