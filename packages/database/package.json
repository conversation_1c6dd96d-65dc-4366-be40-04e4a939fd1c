{"name": "@repo/database", "version": "0.0.0", "private": true, "types": "src/index.ts", "main": "src/index.ts", "exports": {"types": "./src/index.ts", "default": "./dist/index.js"}, "type": "commonjs", "scripts": {"dev": "tsc --watch --preserveWatchOutput", "build": "tsc", "db:migrate": "prisma migrate dev --skip-generate", "db:generate": "prisma generate", "db:deploy": "prisma db push"}, "devDependencies": {"@repo/eslint-config": "workspace:*", "@repo/typescript-config": "workspace:*", "@types/node": "^22.15.3", "eslint": "^9.29.0", "typescript": "5.8.2", "zod-prisma-types": "^3.2.4"}, "dependencies": {"@prisma/client": "6.10.1", "@repo/config": "workspace:*", "@supabase/supabase-js": "^2.50.2", "prisma": "^6.10.1"}}