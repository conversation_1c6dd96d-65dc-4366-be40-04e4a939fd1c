{"name": "@repo/package-template", "version": "0.0.0", "private": true, "type": "commonjs", "types": "src/index.ts", "main": "src/index.ts", "exports": {"types": "./src/index.ts", "default": "./dist/index.js"}, "scripts": {"build": "tsc", "dev": "tsc --watch --preserveWatchOutput", "lint": "eslint .", "clean": "rm -rf dist", "check-types": "tsc --noEmit"}, "devDependencies": {"@repo/eslint-config": "workspace:*", "@repo/typescript-config": "workspace:*", "@types/node": "^22.15.3", "eslint": "^9.29.0", "typescript": "5.8.2"}}