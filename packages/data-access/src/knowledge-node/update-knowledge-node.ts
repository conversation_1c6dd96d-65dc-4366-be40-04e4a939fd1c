import { z } from 'zod';
import { prisma } from '@repo/database';
import type { KnowledgeNode } from '@repo/database';
import { NodeTypeSchema, SourceTypeSchema, NullableJsonValue } from '@repo/entities';

// Create update schema with proper Prisma input types
// Excluding: id, createdAt, updatedAt, authorId (these are managed automatically or are foreign keys)
const KnowledgeNodeUpdateFieldsSchema = z.object({
  content: z.string().optional(),
  embedding: z.number().array().optional(),
  type: NodeTypeSchema.optional(),
  source: SourceTypeSchema.optional(),
  context: NullableJsonValue.optional(),
  validated: z.boolean().optional(),
}).partial();

export type KnowledgeNodeUpdateFields = z.infer<typeof KnowledgeNodeUpdateFieldsSchema>;

export async function updateKnowledgeNode(id: string, update: KnowledgeNodeUpdateFields): Promise<KnowledgeNode> {
  // Validate the update fields
  const validatedUpdate = KnowledgeNodeUpdateFieldsSchema.parse(update);
  
  return await prisma.knowledgeNode.update({
    where: { id },
    data: validatedUpdate ?? undefined,
  });
}
