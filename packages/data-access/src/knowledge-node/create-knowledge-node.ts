import { z } from 'zod';
import {
  prisma,
  KnowledgeNode,
} from '@repo/database';
import {
  NodeTypeSchema,
  SourceTypeSchema,
  NullableJsonValue,
} from '@repo/entities';

// Create schema with proper Prisma input types
// Excluding: id (auto-generated with cuid), createdAt, updatedAt (auto-managed)
// Including: content, embedding, type, source, context, validated, authorId
const KnowledgeNodeCreateFieldsSchema = z.object({
  content: z.string(),
  embedding: z.number().array().optional(),
  type: NodeTypeSchema,
  source: SourceTypeSchema,
  context: NullableJsonValue.optional(),
  validated: z.boolean().optional(),
  authorId: z.string(),
});

export type KnowledgeNodeCreateFields = z.infer<typeof KnowledgeNodeCreateFieldsSchema>;

export async function createKnowledgeNode(createFields: KnowledgeNodeCreateFields): Promise<KnowledgeNode> {
  // Validate the create fields
  const validatedFields = KnowledgeNodeCreateFieldsSchema.parse(createFields);
  
  // @ts-expect-error todo move the embeddings to a different collection please
  return await prisma.knowledgeNode.create({
    data: validatedFields,
  });
}
