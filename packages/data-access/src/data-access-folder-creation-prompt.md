For each the schema defined in schema.prisma called {{insert prisma schema name}}, write a set of data access functions in packages/data-access/src.

Create a folder for the entity.

Inside of the folder, there should be the following functions:
- get{EntityName}ById(id: string)
- get{Enti<PERSON>}({}: FilterFields)
- create{EntityName}(createFields: CreateFields)
- getMany{EntityName}s(filter: FilterFields)
- delete{EntityName}ById(id: string)
- update{EntityName}(id:string, update: UpdateFields)

The UpdateFields and CreateFields and FilterFields should be validated by a zod schema. Import the corresponding zod schema for that entity from '../../../generated/client', then choose a subset of fields from that zod schema that should be considered editable. Then, the type for those fields should be z.inferType<that subset that you just chose>

Do you understand? Please write up a plan for me to approve.