import { z } from 'zod';
import { prisma } from '@repo/database';
import { SessionMessageSchema } from '@repo/entities';
import type { SessionMessage } from '@repo/database';

// Create schema by picking only fields needed for creation
// Excluding: id (auto-generated), createdAt (auto-managed)
const SessionMessageCreateFieldsSchema = SessionMessageSchema.pick({
  sessionId: true,
  senderId: true,
  role: true,
  content: true,
});

export type SessionMessageCreateFields = z.infer<typeof SessionMessageCreateFieldsSchema>;

export async function createSessionMessage(createFields: SessionMessageCreateFields): Promise<SessionMessage> {
  // Validate the create fields
  const validatedFields = SessionMessageCreateFieldsSchema.parse(createFields);
  
  return await prisma.sessionMessage.create({
    data: validatedFields,
  });
}
