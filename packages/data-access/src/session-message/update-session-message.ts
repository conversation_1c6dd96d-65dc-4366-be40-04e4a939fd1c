import { z } from 'zod';
import { prisma } from '@repo/database';
import { SessionMessageSchema } from '@repo/entities';
import type { SessionMessage } from '@repo/database';

// Create update schema by picking only editable fields from SessionMessageSchema
// Excluding: id, createdAt (these are managed automatically)
const SessionMessageUpdateFieldsSchema = SessionMessageSchema.pick({
  sessionId: true,
  senderId: true,
  role: true,
  content: true,
}).partial();

export type SessionMessageUpdateFields = z.infer<typeof SessionMessageUpdateFieldsSchema>;

export async function updateSessionMessage(id: string, updateFields: SessionMessageUpdateFields): Promise<SessionMessage> {
  // Validate the update fields
  const validatedFields = SessionMessageUpdateFieldsSchema.parse(updateFields);
  
  return await prisma.sessionMessage.update({
    where: { id },
    data: validatedFields,
  });
}
