import { z } from 'zod';
import { prisma } from '@repo/database';
import { AgentSchema } from '@repo/entities';
import type { Agent } from '@repo/database';

// Create schema by picking only fields needed for creation
// Excluding: id (auto-generated), createdAt, updatedAt (auto-managed)
const AgentCreateFieldsSchema = AgentSchema.pick({
  name: true,
  description: true,
  povSummary: true,
  ownerId: true,
});

export type AgentCreateFields = z.infer<typeof AgentCreateFieldsSchema>;

export async function createAgent(createFields: AgentCreateFields): Promise<Agent> {
  // Validate the create fields
  const validatedFields = AgentCreateFieldsSchema.parse(createFields);
  
  return await prisma.agent.create({
    data: validatedFields,
  });
}
