import { z } from 'zod';
import { prisma } from '@repo/database';
import { AgentSchema } from '@repo/entities';
import type { Agent } from '@repo/database';

// Create update schema by picking only editable fields from AgentSchema
// Excluding: id, createdAt, updatedAt (these are managed automatically)
const AgentUpdateFieldsSchema = AgentSchema.pick({
  name: true,
  description: true,
  povSummary: true,
  ownerId: true,
}).partial();

export type AgentUpdateFields = z.infer<typeof AgentUpdateFieldsSchema>;

export async function updateAgent(id: string, updateFields: AgentUpdateFields): Promise<Agent> {
  // Validate the update fields
  const validatedFields = AgentUpdateFieldsSchema.parse(updateFields);
  
  return await prisma.agent.update({
    where: { id },
    data: validatedFields,
  });
}
