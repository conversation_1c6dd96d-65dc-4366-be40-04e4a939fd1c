import { prisma } from '@repo/database';

export type QnAPairEmbeddingResult = {
  id: string;
  qnaPairId: string;
  vector: number[];
  model: string;
  createdAt: Date;
};

export type QnAPairEmbeddingFilter = {
  id?: string;
  qnaPairId?: string;
  model?: string;
};

export async function getOneQnAPairEmbedding(filter: QnAPairEmbeddingFilter): Promise<QnAPairEmbeddingResult | null> {
  const conditions: string[] = [];
  const values: any[] = [];
  
  if (filter.id) {
    conditions.push(`id = $${values.length + 1}`);
    values.push(filter.id);
  }
  
  if (filter.qnaPairId) {
    conditions.push(`"qnaPairId" = $${values.length + 1}`);
    values.push(filter.qnaPairId);
  }
  
  if (filter.model) {
    conditions.push(`model = $${values.length + 1}`);
    values.push(filter.model);
  }
  
  if (conditions.length === 0) {
    return null;
  }
  
  const whereClause = conditions.join(' AND ');
  
  const result = await prisma.$queryRawUnsafe(`
    SELECT id, "qnaPairId", vector, model, "createdAt"
    FROM "extensions"."QnAPairEmbedding"
    WHERE ${whereClause}
    LIMIT 1
  `, ...values) as Array<{
    id: string;
    qnaPairId: string;
    vector: number[];
    model: string;
    createdAt: Date;
  }>;
  
  return result[0] || null;
}
