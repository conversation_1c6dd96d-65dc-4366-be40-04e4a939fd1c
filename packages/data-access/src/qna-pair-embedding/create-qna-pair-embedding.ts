import { z } from 'zod';
import { prisma } from '@repo/database';
import { createId } from '@paralleldrive/cuid2';

// Create schema for QnAPairEmbedding creation
// Note: Using raw SQL due to Unsupported vector field
const QnAPairEmbeddingCreateFieldsSchema = z.object({
  qnaPairId: z.string(),
  vector: z.array(z.number()),
  model: z.string(),
});

export type QnAPairEmbeddingCreateFields = z.infer<typeof QnAPairEmbeddingCreateFieldsSchema>;

export async function createQnAPairEmbedding(createFields: QnAPairEmbeddingCreateFields): Promise<{ id: string }> {
  // Validate the create fields
  const validatedFields = QnAPairEmbeddingCreateFieldsSchema.parse(createFields);
  
  // Convert embedding array to PostgreSQL array format
  const embeddingString = `[${validatedFields.vector.join(',')}]`;
  
  const id = createId();
  
  await prisma.$executeRaw`
    INSERT INTO "extensions"."QnAPairEmbedding"
    ("id", "qnaPairId", "vector", "model", "createdAt")
    VALUES (
      ${id},
      ${validatedFields.qnaPairId},
      ${embeddingString}::extensions.vector,
      ${validatedFields.model},
      NOW()
    )
  `;
  
  return { id };
}
