import { prisma } from '@repo/database';

export type QnAPairEmbeddingResult = {
  id: string;
  qnaPairId: string;
  vector: number[];
  model: string;
  createdAt: Date;
};

export async function getQnAPairEmbeddingById(id: string): Promise<QnAPairEmbeddingResult | null> {
  const result = await prisma.$queryRaw`
    SELECT id, "qnaPairId", vector, model, "createdAt"
    FROM "extensions"."QnAPairEmbedding"
    WHERE id = ${id}
  ` as Array<{
    id: string;
    qnaPairId: string;
    vector: number[];
    model: string;
    createdAt: Date;
  }>;
  
  return result[0] || null;
}
