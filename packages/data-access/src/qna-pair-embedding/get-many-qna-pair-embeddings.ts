import { prisma } from '@repo/database';

export type QnAPairEmbeddingResult = {
  id: string;
  qnaPairId: string;
  vector: number[];
  model: string;
  createdAt: Date;
};

export type QnAPairEmbeddingFilter = {
  qnaPairId?: string;
  model?: string;
};

export async function getManyQnAPairEmbeddings(filter?: QnAPairEmbeddingFilter): Promise<QnAPairEmbeddingResult[]> {
  const conditions: string[] = [];
  const values: any[] = [];
  
  if (filter?.qnaPairId) {
    conditions.push(`"qnaPairId" = $${values.length + 1}`);
    values.push(filter.qnaPairId);
  }
  
  if (filter?.model) {
    conditions.push(`model = $${values.length + 1}`);
    values.push(filter.model);
  }
  
  const whereClause = conditions.length > 0 ? `WHERE ${conditions.join(' AND ')}` : '';
  
  const result = await prisma.$queryRawUnsafe(`
    SELECT id, "qnaPairId", vector, model, "createdAt"
    FROM "extensions"."QnAPairEmbedding"
    ${whereClause}
    ORDER BY "createdAt" DESC
  `, ...values) as Array<{
    id: string;
    qnaPairId: string;
    vector: number[];
    model: string;
    createdAt: Date;
  }>;
  
  return result;
}
