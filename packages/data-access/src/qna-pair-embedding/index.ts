export { getQnAPairEmbeddingById } from './get-qna-pair-embedding-by-id';
export { getOneQnAPairEmbedding, type QnAPairEmbeddingFilter } from './get-one-qna-pair-embedding';
export { getManyQnAPairEmbeddings, type QnAPairEmbeddingResult } from './get-many-qna-pair-embeddings';
export { createQnAPairEmbedding, type QnAPairEmbeddingCreateFields } from './create-qna-pair-embedding';
export { deleteQnAPairEmbeddingById } from './delete-qna-pair-embedding-by-id';
export { updateQnAPairEmbedding, type QnAPairEmbeddingUpdateFields } from './update-qna-pair-embedding';
