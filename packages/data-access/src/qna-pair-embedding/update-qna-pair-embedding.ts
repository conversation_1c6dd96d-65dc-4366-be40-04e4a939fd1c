import { z } from 'zod';
import { prisma } from '@repo/database';

// Create update schema for QnAPairEmbedding
// Note: Using raw SQL due to Unsupported vector field
const QnAPairEmbeddingUpdateFieldsSchema = z.object({
  qnaPairId: z.string().optional(),
  vector: z.array(z.number()).optional(),
  model: z.string().optional(),
}).partial();

export type QnAPairEmbeddingUpdateFields = z.infer<typeof QnAPairEmbeddingUpdateFieldsSchema>;

export async function updateQnAPairEmbedding(id: string, updateFields: QnAPairEmbeddingUpdateFields): Promise<{ id: string }> {
  // Validate the update fields
  const validatedFields = QnAPairEmbeddingUpdateFieldsSchema.parse(updateFields);
  
  const updates: string[] = [];
  const values: any[] = [];
  
  if (validatedFields.qnaPairId !== undefined) {
    updates.push(`"qnaPairId" = $${values.length + 1}`);
    values.push(validatedFields.qnaPairId);
  }
  
  if (validatedFields.vector !== undefined) {
    const embeddingString = `[${validatedFields.vector.join(',')}]`;
    updates.push(`vector = $${values.length + 1}::extensions.vector`);
    values.push(embeddingString);
  }
  
  if (validatedFields.model !== undefined) {
    updates.push(`model = $${values.length + 1}`);
    values.push(validatedFields.model);
  }
  
  if (updates.length === 0) {
    return { id };
  }
  
  values.push(id); // Add id as the last parameter
  
  await prisma.$queryRawUnsafe(`
    UPDATE "extensions"."QnAPairEmbedding"
    SET ${updates.join(', ')}
    WHERE id = $${values.length}
  `, ...values);
  
  return { id };
}
