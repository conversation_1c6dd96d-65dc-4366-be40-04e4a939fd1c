import { z } from 'zod';
import { prisma } from '@repo/database';
import { SessionSchema } from '@repo/entities';
import type { Session } from '@repo/database';

// Create update schema by picking only editable fields from SessionSchema
// Excluding: id, createdAt, updatedAt (these are managed automatically)
const SessionUpdateFieldsSchema = SessionSchema.pick({
  agentId: true,
  status: true,
}).partial();

export type SessionUpdateFields = z.infer<typeof SessionUpdateFieldsSchema>;

export async function updateSession(id: string, updateFields: SessionUpdateFields): Promise<Session> {
  // Validate the update fields
  const validatedFields = SessionUpdateFieldsSchema.parse(updateFields);
  
  return await prisma.session.update({
    where: { id },
    data: validatedFields,
  });
}
