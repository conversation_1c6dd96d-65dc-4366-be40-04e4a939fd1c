import { z } from 'zod';
import { prisma } from '@repo/database';
import type { Validation } from '@repo/database';
import { ValidationSchema } from '@repo/entities';

// Create update schema by picking only editable fields from ValidationSchema
// Excluding: id, createdAt, updatedAt (these are managed automatically)
// Including: nodeId, expertId, notes (these can be changed)
const ValidationUpdateFieldsSchema = ValidationSchema.pick({
  nodeId: true,
  expertId: true,
  notes: true,
});

export type ValidationUpdateFields = z.infer<typeof ValidationUpdateFieldsSchema>;

export async function updateValidation(id: string, update: ValidationUpdateFields): Promise<Validation> {
  // Validate the update fields
  const validatedUpdate = ValidationUpdateFieldsSchema.parse(update);
  
  return await prisma.validation.update({
    where: { id },
    data: validatedUpdate,
  });
}
