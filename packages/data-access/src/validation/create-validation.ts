import { z } from 'zod';
import { prisma } from '@repo/database';
import type { Validation } from '@repo/database';
import { ValidationSchema } from '@repo/entities';

// Create schema by picking only fields needed for creation
// Excluding: id (auto-generated with cuid), createdAt, updatedAt (auto-managed)
// Including: nodeId, expertId, notes
const ValidationCreateFieldsSchema = ValidationSchema.pick({
  nodeId: true,
  expertId: true,
  notes: true,
});

export type ValidationCreateFields = z.infer<typeof ValidationCreateFieldsSchema>;

export async function createValidation(createFields: ValidationCreateFields): Promise<Validation> {
  // Validate the create fields
  const validatedFields = ValidationCreateFieldsSchema.parse(createFields);
  
  return await prisma.validation.create({
    data: validatedFields,
  });
}
