import { z } from 'zod';
import { prisma } from '@repo/database';
import { FooSchema } from '@repo/entities';
import type { Foo } from '@repo/database';

// Create schema by picking only fields needed for creation
// Excluding: id (auto-generated)
const FooCreateFieldsSchema = FooSchema.pick({
  name: true,
  email: true,
});

export type FooCreateFields = z.infer<typeof FooCreateFieldsSchema>;

export async function createFoo(createFields: FooCreateFields): Promise<Foo> {
  // Validate the create fields
  const validatedFields = FooCreateFieldsSchema.parse(createFields);
  
  return await prisma.foo.create({
    data: validatedFields,
  });
}
