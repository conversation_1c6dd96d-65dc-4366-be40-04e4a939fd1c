import { z } from 'zod';
import { prisma } from '@repo/database';
import type { Foo } from '@repo/database';
import { FooSchema } from '@repo/entities';

// Create update schema by picking only editable fields from FooSchema
const FooUpdateFieldsSchema = FooSchema.pick({
  name: true,
  email: true,
});

export type FooUpdateFields = z.infer<typeof FooUpdateFieldsSchema>;

export async function updateFoo(id: number, update: FooUpdateFields): Promise<Foo> {
  // Validate the update fields
  const validatedUpdate = FooUpdateFieldsSchema.parse(update);
  
  return await prisma.foo.update({
    where: { id },
    data: validatedUpdate,
  });
}
