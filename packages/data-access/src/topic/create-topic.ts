import { z } from 'zod';
import {prisma} from '@repo/database';
import type {Topic} from '@repo/database';
import { TopicSchema } from '@repo/entities';

// Create schema by picking only fields needed for creation
// Excluding: id (auto-generated with cuid), createdAt, updatedAt (auto-managed)
// Including: name
const TopicCreateFieldsSchema = TopicSchema.pick({
  name: true,
});

export type TopicCreateFields = z.infer<typeof TopicCreateFieldsSchema>;

export async function createTopic(createFields: TopicCreateFields): Promise<Topic> {
  // Validate the create fields
  const validatedFields = TopicCreateFieldsSchema.parse(createFields);
  
  return await prisma.topic.create({
    data: validatedFields,
  });
}
