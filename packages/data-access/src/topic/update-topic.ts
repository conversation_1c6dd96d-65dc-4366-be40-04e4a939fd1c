import { z } from 'zod';
import { prisma } from '@repo/database';
import type { Topic } from '@repo/database';
import { TopicSchema } from '@repo/entities';

// Create update schema by picking only editable fields from TopicSchema
// Excluding: id, createdAt, updatedAt (these are managed automatically)
// Including: name (the main editable field for topics)
const TopicUpdateFieldsSchema = TopicSchema.pick({
  name: true,
});

export type TopicUpdateFields = z.infer<typeof TopicUpdateFieldsSchema>;

export async function updateTopic(id: string, update: TopicUpdateFields): Promise<Topic> {
  // Validate the update fields
  const validatedUpdate = TopicUpdateFieldsSchema.parse(update);
  
  return await prisma.topic.update({
    where: { id },
    data: validatedUpdate,
  });
}
