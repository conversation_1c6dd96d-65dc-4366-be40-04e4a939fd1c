import { z } from 'zod';
import { prisma } from '@repo/database';
import type { User } from '@repo/database';
import { UserSchema } from '@repo/entities';

// Create update schema by picking only editable fields from UserSchema
// Note: userId might be editable depending on business requirements
const UserUpdateFieldsSchema = UserSchema.pick({
  userId: true,
});

export type UserUpdateFields = z.infer<typeof UserUpdateFieldsSchema>;

export async function updateUser(id: string, update: UserUpdateFields): Promise<User> {
  // Validate the update fields
  const validatedUpdate = UserUpdateFieldsSchema.parse(update);
  
  return await prisma.user.update({
    where: { userId: id },
    data: validatedUpdate,
  });
}
