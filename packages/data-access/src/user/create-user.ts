import { z } from 'zod';
import { prisma } from '@repo/database';
import type { User } from '@repo/database';
import { UserSchema } from '@repo/entities';

// Create schema by picking only fields needed for creation
// Including: userId (required for User creation)
const UserCreateFieldsSchema = UserSchema.pick({
  userId: true,
});

export type UserCreateFields = z.infer<typeof UserCreateFieldsSchema>;

export async function createUser(createFields: UserCreateFields): Promise<User> {
  // Validate the create fields
  const validatedFields = UserCreateFieldsSchema.parse(createFields);
  
  return await prisma.user.create({
    data: validatedFields,
  });
}
