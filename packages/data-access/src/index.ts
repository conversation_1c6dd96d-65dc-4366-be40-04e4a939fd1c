// most of this folder is written by an agent
// check out data-access-folder-creation-prompt.md

// Foo data access functions
export {
  getFooById,
  getOneFoo,
  getMany<PERSON>oos,
  createFoo,
  deleteFooById,
  updateFoo,
  type FooCreateFields,
  type FooUpdateFields,
} from './foo';

// User data access functions
export {
  getUserById,
  getOneUser,
  getManyUsers,
  createUser,
  deleteUserById,
  updateUser,
  type UserCreateFields,
  type UserUpdateFields,
} from './user';

// KnowledgeNode data access functions
export {
  getKnowledgeNodeById,
  getOneKnowledgeNode,
  getManyKnowledgeNodes,
  createKnowledgeNode,
  deleteKnowledgeNodeById,
  updateKnowledgeNode,
  type KnowledgeNodeCreateFields,
  type KnowledgeNodeUpdateFields,
} from './knowledge-node';

// NodeRelationship data access functions
export {
  getNodeRelationshipById,
  getOneNodeRelationship,
  getManyNodeRelationships,
  createNodeRelationship,
  deleteNodeRelationshipById,
  updateNodeRelationship,
  type NodeRela<PERSON>hipCreateFields,
  type NodeRelationshipUpdateFields,
} from './node-relationship';

// Validation data access functions
export {
  getValidationById,
  getOneValidation,
  getManyValidations,
  createValidation,
  deleteValidationById,
  updateValidation,
  type ValidationCreateFields,
  type ValidationUpdateFields,
} from './validation';

// Topic data access functions
export {
  getTopicById,
  getOneTopic,
  getManyTopics,
  createTopic,
  deleteTopicById,
  updateTopic,
  type TopicCreateFields,
  type TopicUpdateFields,
} from './topic';
