// most of this folder is written by an agent
// check out data-access-folder-creation-prompt.md

// Foo data access functions
export {
  getFooById,
  getOneFoo,
  getMany<PERSON>oos,
  createFoo,
  deleteFooById,
  updateFoo,
  type FooCreateFields,
  type FooUpdateFields,
} from './foo';

// User data access functions
export {
  getUserById,
  getOneUser,
  getManyUsers,
  createUser,
  deleteUserById,
  updateUser,
  type UserCreateFields,
  type UserUpdateFields,
} from './user';

// KnowledgeNode data access functions
export {
  getKnowledgeNodeById,
  getOneKnowledgeNode,
  getManyKnowledgeNodes,
  createKnowledgeNode,
  deleteKnowledgeNodeById,
  updateKnowledgeNode,
  type KnowledgeNodeCreateFields,
  type KnowledgeNodeUpdateFields,
} from './knowledge-node';

// NodeRelationship data access functions
export {
  getNodeRelationshipById,
  getOneNodeRelationship,
  getManyNodeRelationships,
  createNodeRelationship,
  deleteNodeRelationshipById,
  updateNodeRelationship,
  type NodeRela<PERSON>hipCreateFields,
  type NodeRelationshipUpdateFields,
} from './node-relationship';

// Validation data access functions
export {
  getValidationById,
  getOneValidation,
  getManyValidations,
  createValidation,
  deleteValidationById,
  updateValidation,
  type ValidationCreateFields,
  type ValidationUpdateFields,
} from './validation';

// Topic data access functions
export {
  getTopicById,
  getOneTopic,
  getManyTopics,
  createTopic,
  deleteTopicById,
  updateTopic,
  type TopicCreateFields,
  type TopicUpdateFields,
} from './topic';

// Agent data access functions
export {
  getAgentById,
  getOneAgent,
  getManyAgents,
  createAgent,
  deleteAgentById,
  updateAgent,
  type AgentCreateFields,
  type AgentUpdateFields,
} from './agent';

// QnAPair data access functions
export {
  getQnAPairById,
  getOneQnAPair,
  getManyQnAPairs,
  createQnAPair,
  deleteQnAPairById,
  updateQnAPair,
  type QnAPairCreateFields,
  type QnAPairUpdateFields,
} from './qna-pair';

// QnAPairEmbedding data access functions
export {
  getQnAPairEmbeddingById,
  getOneQnAPairEmbedding,
  getManyQnAPairEmbeddings,
  createQnAPairEmbedding,
  deleteQnAPairEmbeddingById,
  updateQnAPairEmbedding,
  type QnAPairEmbeddingCreateFields,
  type QnAPairEmbeddingUpdateFields,
  type QnAPairEmbeddingResult,
  type QnAPairEmbeddingFilter,
} from './qna-pair-embedding';

// Session data access functions
export {
  getSessionById,
  getOneSession,
  getManySessions,
  createSession,
  deleteSessionById,
  updateSession,
  type SessionCreateFields,
  type SessionUpdateFields,
} from './session';

// SessionMessage data access functions
export {
  getSessionMessageById,
  getOneSessionMessage,
  getManySessionMessages,
  createSessionMessage,
  deleteSessionMessageById,
  updateSessionMessage,
  type SessionMessageCreateFields,
  type SessionMessageUpdateFields,
} from './session-message';

// SessionParticipant data access functions
export {
  getSessionParticipantById,
  getOneSessionParticipant,
  getManySessionParticipants,
  createSessionParticipant,
  deleteSessionParticipantById,
  updateSessionParticipant,
  type SessionParticipantCreateFields,
  type SessionParticipantUpdateFields,
} from './session-participant';
