import { z } from 'zod';
import { prisma } from '@repo/database';
import type { NodeRelationship } from '@repo/database';
import { NodeRelationshipSchema } from '@repo/entities';

// Create update schema by picking only editable fields from NodeRelationshipSchema
// Excluding: id, createdAt, updatedAt (these are managed automatically)
// Including: fromId, toId, type (these can be changed to modify relationships)
const NodeRelationshipUpdateFieldsSchema = NodeRelationshipSchema.pick({
  fromId: true,
  toId: true,
  type: true,
});

export type NodeRelationshipUpdateFields = z.infer<typeof NodeRelationshipUpdateFieldsSchema>;

export async function updateNodeRelationship(id: string, update: NodeRelationshipUpdateFields): Promise<NodeRelationship> {
  // Validate the update fields
  const validatedUpdate = NodeRelationshipUpdateFieldsSchema.parse(update);
  
  return await prisma.nodeRelationship.update({
    where: { id },
    data: validatedUpdate,
  });
}
