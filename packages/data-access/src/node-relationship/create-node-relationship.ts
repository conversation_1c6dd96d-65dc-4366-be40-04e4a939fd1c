import { z } from 'zod';
import { prisma } from '@repo/database';
import type { NodeRelationship } from '@repo/database';
import { NodeRelationshipSchema } from '@repo/entities';

// Create schema by picking only fields needed for creation
// Excluding: id (auto-generated with cuid), createdAt, updatedAt (auto-managed)
// Including: fromId, toId, type
const NodeRelationshipCreateFieldsSchema = NodeRelationshipSchema.pick({
  fromId: true,
  toId: true,
  type: true,
});

export type NodeRelationshipCreateFields = z.infer<typeof NodeRelationshipCreateFieldsSchema>;

export async function createNodeRelationship(createFields: NodeRelationshipCreateFields): Promise<NodeRelationship> {
  // Validate the create fields
  const validatedFields = NodeRelationshipCreateFieldsSchema.parse(createFields);
  
  return await prisma.nodeRelationship.create({
    data: validatedFields,
  });
}
