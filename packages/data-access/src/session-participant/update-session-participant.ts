import { z } from 'zod';
import { prisma } from '@repo/database';
import { SessionParticipantSchema } from '@repo/entities';
import type { SessionParticipant } from '@repo/database';

// Create update schema by picking only editable fields from SessionParticipantSchema
// Excluding: id, joinedAt (these are managed automatically)
const SessionParticipantUpdateFieldsSchema = SessionParticipantSchema.pick({
  sessionId: true,
  userId: true,
  role: true,
}).partial();

export type SessionParticipantUpdateFields = z.infer<typeof SessionParticipantUpdateFieldsSchema>;

export async function updateSessionParticipant(id: string, updateFields: SessionParticipantUpdateFields): Promise<SessionParticipant> {
  // Validate the update fields
  const validatedFields = SessionParticipantUpdateFieldsSchema.parse(updateFields);
  
  return await prisma.sessionParticipant.update({
    where: { id },
    data: validatedFields,
  });
}
