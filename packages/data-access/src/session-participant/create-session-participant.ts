import { z } from 'zod';
import { prisma } from '@repo/database';
import { SessionParticipantSchema } from '@repo/entities';
import type { SessionParticipant } from '@repo/database';

// Create schema by picking only fields needed for creation
// Excluding: id (auto-generated), joinedAt (auto-managed)
const SessionParticipantCreateFieldsSchema = SessionParticipantSchema.pick({
  sessionId: true,
  userId: true,
  role: true,
});

export type SessionParticipantCreateFields = z.infer<typeof SessionParticipantCreateFieldsSchema>;

export async function createSessionParticipant(createFields: SessionParticipantCreateFields): Promise<SessionParticipant> {
  // Validate the create fields
  const validatedFields = SessionParticipantCreateFieldsSchema.parse(createFields);
  
  return await prisma.sessionParticipant.create({
    data: validatedFields,
  });
}
