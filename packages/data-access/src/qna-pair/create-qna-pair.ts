import { z } from 'zod';
import { prisma } from '@repo/database';
import { QnAPairSchema } from '@repo/entities';
import type { QnAPair } from '@repo/database';

// Create schema by picking only fields needed for creation
// Excluding: id (auto-generated), createdAt (auto-managed)
const QnAPairCreateFieldsSchema = QnAPairSchema.pick({
  agentId: true,
  question: true,
  answer: true,
  persona: true,
});

export type QnAPairCreateFields = z.infer<typeof QnAPairCreateFieldsSchema>;

export async function createQnAPair(createFields: QnAPairCreateFields): Promise<QnAPair> {
  // Validate the create fields
  const validatedFields = QnAPairCreateFieldsSchema.parse(createFields);
  
  return await prisma.qnAPair.create({
    data: validatedFields,
  });
}
