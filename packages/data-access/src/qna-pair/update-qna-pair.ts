import { z } from 'zod';
import { prisma } from '@repo/database';
import { QnAPairSchema } from '@repo/entities';
import type { QnAPair } from '@repo/database';

// Create update schema by picking only editable fields from QnAPairSchema
// Excluding: id, createdAt (these are managed automatically)
const QnAPairUpdateFieldsSchema = QnAPairSchema.pick({
  agentId: true,
  question: true,
  answer: true,
  persona: true,
}).partial();

export type QnAPairUpdateFields = z.infer<typeof QnAPairUpdateFieldsSchema>;

export async function updateQnAPair(id: string, updateFields: QnAPairUpdateFields): Promise<QnAPair> {
  // Validate the update fields
  const validatedFields = QnAPairUpdateFieldsSchema.parse(updateFields);
  
  return await prisma.qnAPair.update({
    where: { id },
    data: validatedFields,
  });
}
