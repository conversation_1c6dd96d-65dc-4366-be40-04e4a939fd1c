{"name": "@repo/data-access", "version": "0.0.0", "private": true, "types": "src/index.ts", "main": "src/index.ts", "exports": {"types": "./src/index.ts", "default": "./dist/index.js"}, "type": "commonjs", "scripts": {"dev": "tsc --watch --preserveWatchOutput", "build": "tsc"}, "devDependencies": {"@repo/eslint-config": "workspace:*", "@repo/typescript-config": "workspace:*", "@types/node": "^22.15.3", "eslint": "^9.29.0", "typescript": "5.8.2"}, "dependencies": {"@paralleldrive/cuid2": "^2.2.2", "@repo/database": "workspace:*", "@repo/entities": "workspace:*", "zod": "^3.25.71"}}