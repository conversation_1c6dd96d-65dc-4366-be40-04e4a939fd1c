import dotenv from 'dotenv';
dotenv.config();

/* eslint-disable turbo/no-undeclared-env-vars */
export const appConfig = {
  nodeEnv: process.env.NODE_ENV || 'development',
  isProduction: process.env.NODE_ENV === 'production',
  isDevelopment: process.env.NODE_ENV === 'development',
  isTest: process.env.NODE_ENV === 'test',

  databaseUrl: process.env.DATABASE_URL || '',

  openAI: {
    apiKey: process.env.OPENAI_API_KEY || '',
  },

  supabase: {
    url: process.env.SUPABASE_URL || '',
    anonKey: process.env.SUPABASE_ANON_KEY || '',
    publicKey: process.env.SUPABASE_PK || '',
    secretKey: process.env.SUPABASE_SK || '',
    serviceRole: process.env.SUPABASE_SERVICE_ROLE || '',
  },

  notion: {
    token: process.env.NOTION_TOKEN || '',
  },
} as const;