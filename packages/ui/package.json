{"name": "@repo/ui", "version": "0.0.0", "private": true, "exports": {"./*": "./src/*.tsx"}, "scripts": {"lint": "eslint .", "generate:component": "turbo gen react-component", "check-types": "tsc --noEmit"}, "devDependencies": {"@repo/eslint-config": "workspace:*", "@repo/typescript-config": "workspace:*", "@turbo/gen": "^2.5.0", "@types/node": "^22.15.3", "@types/react": "19.1.0", "@types/react-dom": "19.1.1", "eslint": "^9.29.0", "typescript": "5.8.2"}, "dependencies": {"react": "^19.1.0", "react-dom": "^19.1.0"}}